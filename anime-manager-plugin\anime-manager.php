<?php
/**
 * Plugin Name: مدير الأنمي - Anime Manager
 * Plugin URI: https://example.com
 * Description: إضافة احترافية لإدارة الأنمي مع تكامل كامل مع قالب Jinko - تتضمن إدارة القصة، صورة الغلاف، النوع، المدة، الحالة، عدد الحلقات، الموسم والروابط الخارجية
 * Version: 1.0.0
 * Author: مطور ووردبريس
 * Text Domain: anime-manager
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تعريف الثوابت
define('ANIME_MANAGER_VERSION', '1.0.0');
define('ANIME_MANAGER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ANIME_MANAGER_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * الكلاس الرئيسي للإضافة
 */
class AnimeManager {
    
    /**
     * المثيل الوحيد للكلاس
     */
    private static $instance = null;
    
    /**
     * الحصول على المثيل الوحيد
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * البناء
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * تهيئة الخطافات
     */
    private function init_hooks() {
        add_action('init', array($this, 'register_anime_post_type'));
        add_action('add_meta_boxes', array($this, 'add_anime_metabox'));
        add_action('save_post', array($this, 'save_anime_meta'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_styles'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_styles'));
        add_filter('the_content', array($this, 'display_anime_details'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }
    
    /**
     * تحميل ملفات الترجمة
     */
    public function load_textdomain() {
        load_plugin_textdomain('anime-manager', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * تسجيل نوع المحتوى المخصص للأنمي
     */
    public function register_anime_post_type() {
        $labels = array(
            'name'                  => __('الأنمي', 'anime-manager'),
            'singular_name'         => __('أنمي', 'anime-manager'),
            'menu_name'             => __('إدارة الأنمي', 'anime-manager'),
            'name_admin_bar'        => __('أنمي', 'anime-manager'),
            'add_new'               => __('إضافة جديد', 'anime-manager'),
            'add_new_item'          => __('إضافة أنمي جديد', 'anime-manager'),
            'new_item'              => __('أنمي جديد', 'anime-manager'),
            'edit_item'             => __('تحرير الأنمي', 'anime-manager'),
            'view_item'             => __('عرض الأنمي', 'anime-manager'),
            'all_items'             => __('جميع الأنمي', 'anime-manager'),
            'search_items'          => __('البحث في الأنمي', 'anime-manager'),
            'parent_item_colon'     => __('الأنمي الأب:', 'anime-manager'),
            'not_found'             => __('لم يتم العثور على أنمي', 'anime-manager'),
            'not_found_in_trash'    => __('لا يوجد أنمي في سلة المهملات', 'anime-manager'),
            'featured_image'        => __('صورة غلاف الأنمي', 'anime-manager'),
            'set_featured_image'    => __('تعيين صورة الغلاف', 'anime-manager'),
            'remove_featured_image' => __('إزالة صورة الغلاف', 'anime-manager'),
            'use_featured_image'    => __('استخدام كصورة غلاف', 'anime-manager'),
            'archives'              => __('أرشيف الأنمي', 'anime-manager'),
            'insert_into_item'      => __('إدراج في الأنمي', 'anime-manager'),
            'uploaded_to_this_item' => __('تم رفعه لهذا الأنمي', 'anime-manager'),
            'filter_items_list'     => __('تصفية قائمة الأنمي', 'anime-manager'),
            'items_list_navigation' => __('التنقل في قائمة الأنمي', 'anime-manager'),
            'items_list'            => __('قائمة الأنمي', 'anime-manager'),
        );
        
        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'anime'),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => 5,
            'menu_icon'          => 'dashicons-format-video',
            'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
            'show_in_rest'       => true,
            'rest_base'          => 'anime',
            'rest_controller_class' => 'WP_REST_Posts_Controller',
        );
        
        register_post_type('anime', $args);
    }
    
    /**
     * إضافة صندوق البيانات الإضافية
     */
    public function add_anime_metabox() {
        add_meta_box(
            'anime_details',
            __('تفاصيل الأنمي', 'anime-manager'),
            array($this, 'render_anime_metabox'),
            'anime',
            'normal',
            'high'
        );
    }
    
    /**
     * عرض صندوق البيانات الإضافية
     */
    public function render_anime_metabox($post) {
        // إضافة nonce للأمان
        wp_nonce_field('anime_meta_nonce', 'anime_meta_nonce_field');
        
        // الحصول على القيم المحفوظة
        $anime_type = get_post_meta($post->ID, '_anime_type', true);
        $anime_duration = get_post_meta($post->ID, '_anime_duration', true);
        $anime_status = get_post_meta($post->ID, '_anime_status', true);
        $anime_episodes = get_post_meta($post->ID, '_anime_episodes', true);
        $anime_season = get_post_meta($post->ID, '_anime_season', true);
        $anime_year = get_post_meta($post->ID, '_anime_year', true);
        $anime_links = get_post_meta($post->ID, '_anime_links', true);
        
        // تعيين القيم الافتراضية
        if (empty($anime_year)) {
            $anime_year = date('Y');
        }
        if (!is_array($anime_links)) {
            $anime_links = array();
        }
        
        if (file_exists(ANIME_MANAGER_PLUGIN_DIR . 'includes/metabox-template.php')) {
            include ANIME_MANAGER_PLUGIN_DIR . 'includes/metabox-template.php';
        }
    }
    
    /**
     * حفظ بيانات الأنمي
     */
    public function save_anime_meta($post_id) {
        // التحقق من nonce
        if (!isset($_POST['anime_meta_nonce_field']) || 
            !wp_verify_nonce($_POST['anime_meta_nonce_field'], 'anime_meta_nonce')) {
            return;
        }
        
        // التحقق من الصلاحيات
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // تجنب الحفظ التلقائي
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // التحقق من نوع المنشور
        if (get_post_type($post_id) !== 'anime') {
            return;
        }
        
        // حفظ البيانات مع التنظيف
        $fields = array(
            '_anime_type' => 'sanitize_text_field',
            '_anime_duration' => 'intval',
            '_anime_status' => 'sanitize_text_field',
            '_anime_episodes' => 'intval',
            '_anime_season' => 'sanitize_text_field',
            '_anime_year' => 'intval'
        );
        
        foreach ($fields as $field => $sanitize_function) {
            if (isset($_POST[$field])) {
                $value = call_user_func($sanitize_function, $_POST[$field]);
                update_post_meta($post_id, $field, $value);
            }
        }
        
        // حفظ الروابط الخارجية
        if (isset($_POST['_anime_links']) && is_array($_POST['_anime_links'])) {
            $links = array();
            foreach ($_POST['_anime_links'] as $link) {
                if (!empty($link['title']) || !empty($link['url'])) {
                    $links[] = array(
                        'title' => sanitize_text_field($link['title']),
                        'url' => esc_url_raw($link['url'])
                    );
                }
            }
            update_post_meta($post_id, '_anime_links', $links);
        } else {
            delete_post_meta($post_id, '_anime_links');
        }
    }
    
    /**
     * تحميل ملفات CSS للواجهة الأمامية
     */
    public function enqueue_frontend_styles() {
        if (is_singular('anime') || is_post_type_archive('anime')) {
            wp_enqueue_style(
                'anime-manager-frontend',
                ANIME_MANAGER_PLUGIN_URL . 'assets/frontend-style.css',
                array(),
                ANIME_MANAGER_VERSION
            );
            
            // تحميل التنسيقات المخصصة لصفحات الأنمي المفردة
            if (is_singular('anime')) {
                wp_enqueue_style(
                    'anime-single-style',
                    ANIME_MANAGER_PLUGIN_URL . 'assets/anime-single-style.css',
                    array('anime-manager-frontend'),
                    ANIME_MANAGER_VERSION
                );
            }
        }
    }
    
    /**
     * تحميل ملفات CSS للوحة الإدارة
     */
    public function enqueue_admin_styles($hook) {
        global $post_type;
        
        if (($hook === 'post.php' || $hook === 'post-new.php') && $post_type === 'anime') {
            wp_enqueue_style(
                'anime-manager-admin',
                ANIME_MANAGER_PLUGIN_URL . 'assets/admin-style.css',
                array(),
                ANIME_MANAGER_VERSION
            );
            
            wp_enqueue_script(
                'anime-manager-admin',
                ANIME_MANAGER_PLUGIN_URL . 'assets/admin-script.js',
                array('jquery'),
                ANIME_MANAGER_VERSION,
                true
            );
        }
    }
    
    /**
     * عرض تفاصيل الأنمي في الواجهة الأمامية
     */
    public function display_anime_details($content) {
        if (!is_singular('anime') || !in_the_loop() || !is_main_query()) {
            return $content;
        }
        
        // تحقق من وجود قالب مخصص للأنمي
        $template = get_page_template_slug();
        if (locate_template('single-anime.php') || $template === 'single-anime.php') {
            // إذا كان هناك قالب مخصص، لا تعرض التفاصيل هنا
            return $content;
        }
        
        global $post;
        
        // الحصول على البيانات
        $anime_type = get_post_meta($post->ID, '_anime_type', true);
        $anime_duration = get_post_meta($post->ID, '_anime_duration', true);
        $anime_status = get_post_meta($post->ID, '_anime_status', true);
        $anime_episodes = get_post_meta($post->ID, '_anime_episodes', true);
        $anime_season = get_post_meta($post->ID, '_anime_season', true);
        $anime_year = get_post_meta($post->ID, '_anime_year', true);
        $anime_links = get_post_meta($post->ID, '_anime_links', true);
        
        // إنشاء HTML لعرض التفاصيل
        ob_start();
        if (file_exists(ANIME_MANAGER_PLUGIN_DIR . 'includes/frontend-display.php')) {
            include ANIME_MANAGER_PLUGIN_DIR . 'includes/frontend-display.php';
        }
        $anime_details = ob_get_clean();
        
        return $anime_details . $content;
    }
}

// تهيئة الإضافة
function anime_manager_init() {
    return AnimeManager::get_instance();
}

// بدء الإضافة
add_action('plugins_loaded', 'anime_manager_init');

// خطاف التفعيل
register_activation_hook(__FILE__, 'anime_manager_activation');
function anime_manager_activation() {
    // تسجيل نوع المحتوى
    AnimeManager::get_instance()->register_anime_post_type();
    
    // إعادة كتابة قواعد URL
    flush_rewrite_rules();
}

// خطاف إلغاء التفعيل
register_deactivation_hook(__FILE__, 'anime_manager_deactivation');
function anime_manager_deactivation() {
    // إعادة كتابة قواعد URL
    flush_rewrite_rules();
}
