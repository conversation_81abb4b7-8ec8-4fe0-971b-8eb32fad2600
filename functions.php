<?php

/**
 * jinko functions and definitions
 *
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0.1
 */

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function tfm_theme_setup() {

	// Make theme available for translation.
	load_theme_textdomain( 'jinko', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	// Let WordPress manage the document title.
	add_theme_support( 'title-tag' );

	// Enable support for Post Thumbnails on posts and pages.
	add_theme_support( 'post-thumbnails' );

	// Add support for full and wide align images.
	add_theme_support( 'align-wide' );

	// Add support for responsive embeds.
	add_theme_support( 'responsive-embeds' );

	// Switch default core markup for search form, comment form, and comments to output valid HTML5.
	add_theme_support( 'html5', array(
		'comment-form',
		'comment-list',
		'gallery',
		'caption',
		'search',
	) );

	// Enable support for Post Formats.
	add_theme_support( 'post-formats',
		array(  
		'gallery',
		'image',
		'video',
		'audio',
		)
	);

	// Add theme support for Custom Logo.
	add_theme_support('custom-logo');

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	// Add theme support for custom background
	add_theme_support( 'custom-background',
		array(
			'default-color' => 'ffffff',
		)
	);

	// Block styles
	add_theme_support( 'wp-block-styles' );

	// Register Menus
	register_nav_menus( array(
		'primary'    => esc_html__( 'Primary Menu', 'jinko' ),
		'split-menu-left'    => esc_html__( 'Split Menu Left Items', 'jinko' ),
		'split-menu-right'    => esc_html__( 'Split Menu Right Items', 'jinko' ),
		'slide-menu-primary'    => esc_html__( 'Toggle Sidebar Primary Menu', 'jinko' ),
		'header-secondary'    => esc_html__( 'Header Secondary Menu', 'jinko' ),
		'footer'     => esc_html__( 'Footer Menu', 'jinko'),
	) );

	// ========================================================
	// Additional image sizes
	// ========================================================
	/**
	 * Not required but helps reduce page load by adding
	 * additional smaller images for specific layouts
	 */

	$site_settings = tfm_general_settings();
	$site_width = get_theme_mod( 'tfm_site_width', $site_settings['site_width'] );

	// Small image size (cols 4)
	add_image_size( 'tfm-small-image', apply_filters( 'tfm_small_image_width', 440 ), 0, false );

	// Mobile image size
	add_image_size( 'tfm-mobile-image', apply_filters( 'tfm_mobile_image_width', 320 ), 0, false );

	add_image_size( 'tfm-single-image', apply_filters( 'tfm_single_image_width', $site_width ), 0, false );

}

add_action( 'after_setup_theme', 'tfm_theme_setup' );

// ========================================================
// Set content width
// ========================================================

if ( ! isset( $content_width ) ) {
	$content_width = 1300;
}

// ========================================================
// Register Widget areas
// ========================================================

function tfm_widgets_init() {

	register_sidebar( array(
		'name'          => esc_html__( 'Static Sidebar', 'jinko' ),
		'id'            => 'sidebar-1',
		'description'   => esc_html__( 'Add widgets here to appear in your static sidebar', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'Slide Out Sidebar', 'jinko' ),
		'id'            => 'sidebar-2',
		'description'   => esc_html__( 'Add widgets here to appear in your slide out sidebar', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );
	// footer columns
	register_sidebar( array(
		'name'          => esc_html__( 'Footer Column 1', 'jinko' ),
		'id'            => 'footer-column-1',
		'description'   => esc_html__( 'Add widgets here to appear in your footer column', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'Footer Column 2', 'jinko' ),
		'id'            => 'footer-column-2',
		'description'   => esc_html__( 'Add widgets here to appear in your footer column', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'Footer Column 3', 'jinko' ),
		'id'            => 'footer-column-3',
		'description'   => esc_html__( 'Add widgets here to appear in your footer column', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'Footer Column 4', 'jinko' ),
		'id'            => 'footer-column-4',
		'description'   => esc_html__( 'Add widgets here to appear in your footer column', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	// in loop between posts

	register_sidebar( array(
		'name'          => esc_html__( 'Home: Between Posts', 'jinko' ),
		'id'            => 'loop-sidebar-home',
		'description'   => esc_html__( 'Add widgets here to appear between posts on the homepage. Set the position of this sidebar in Appearance > Theme Settings > Homepage Settings', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'Category: Between Posts', 'jinko' ),
		'id'            => 'loop-sidebar-category',
		'description'   => esc_html__( 'Add widgets here to appear between posts in category pages. Set the position of this sidebar in Appearance > Theme Settings > Archive Settings', 'jinko' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );
	register_sidebar( array(
		'name'          => esc_html__( 'Single: After Content', 'jinko' ),
		'id'            => 'after-content',
		'description'   => esc_html__( 'Add widgets here to appear ater single post content', 'jinko' ),
		'before_widget' => '<div id="%1$s" class="widget after-content-widget %2$s">',
		'after_widget'  => '</div>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );
	
}
add_action( 'widgets_init', 'tfm_widgets_init' );

// ========================================================
// Enqueue Google Fonts
// ========================================================

if ( ! function_exists( 'tfm_fonts_url' ) ) {

	function tfm_fonts_url( $font ) {

		$fonts_url = '';
		 
		 /*
	    Translators: If there are characters in your language that are not supported
	    by chosen font(s), translate this to 'off'. Do not translate into your own language.
	     */
	    if ( 'off' !== _x( 'on', 'Google font: on or off', 'jinko' ) ) {
	    	if ($font === 'jost') {
	    		if ( apply_filters( 'tfm_legacy_gfont', false)) {
	    			// Useful for OMGF plugin
	    			$fonts_url = add_query_arg( 'family', 'Jost:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700', "https://fonts.googleapis.com/css2" );
	    		} else {
	    			$fonts_url = add_query_arg( 'family', 'Jost:ital,wght@0,100..900;1,100..900&display=swap', "https://fonts.googleapis.com/css2" );
	    		}


		    }
	    }
		 
		return esc_url_raw( $fonts_url );

	}

}

// ========================================================
// Enqueue scripts and styles
// ========================================================

if ( ! function_exists( 'tfm_scripts' ) ) {

	function tfm_scripts() {

		// Get Theme Version.
		$theme_version = wp_get_theme()->get( 'Version' );
		
		// CSS
		wp_enqueue_style('normalize', get_template_directory_uri() . '/css/normalize.css', array(), '1.0.0', 'all');
		wp_enqueue_style('fontello', get_template_directory_uri() . '/css/fontello/css/fontello.css', array(), null );
		wp_enqueue_style( 'tfm-google-font-jost', tfm_fonts_url('jost'), array(), '1.0.0' );
		wp_enqueue_style('tfm-core-style', get_template_directory_uri() . '/style.css', array(), $theme_version, 'all');
		wp_enqueue_style('tfm-gutenberg-style', get_template_directory_uri() . '/css/gutenberg.css', array(), '1.0.0', 'all');

		// Load Masonry
		if ( 'masonry' === tfm_get_post_layout() )  {
			wp_enqueue_script( 'masonry');
			wp_enqueue_script( 'tfm-masonry-init', get_template_directory_uri() . '/js/masonry-init.js', array(), null, true);
		}

		// Main JS
		wp_enqueue_script( 'tfm-main', get_template_directory_uri() . '/js/main.js', array( 'jquery' ), '1.0.0', array( 'in_footer' => true));

		// Comments
		if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
			wp_enqueue_script( 'comment-reply' );
		}

	}
}
add_action( 'wp_enqueue_scripts', 'tfm_scripts' );

// ========================================================
// Enqueue Gutenberg Editor scripts and styles
// ========================================================

function tfm_gutenberg_styles() {

	if ( is_admin()) {

		 wp_enqueue_style('fontello', get_template_directory_uri() . '/css/fontello/css/fontello.css', array(), null );
		 wp_enqueue_style( 'tfm-google-font-jost', tfm_fonts_url('jost'), array(), '1.0.0' );
		 wp_enqueue_style( 'tfm-gutenberg-editor', get_template_directory_uri() . '/css/gutenberg-editor-style.css', false, '1.0.0', 'all' );

	}

}
add_action( 'enqueue_block_assets', 'tfm_gutenberg_styles' );

// ========================================================
// Custom classes added to body class array
// ========================================================

if ( ! function_exists( 'tfm_body_classes' ) ) {

	function tfm_body_classes( $classes ) {

		if ( ( is_single( ) && get_theme_mod( 'tfm_single_sidebar', false ) || is_home() && get_theme_mod( 'tfm_homepage_sidebar', true ) || ( is_archive() || is_search() ) && get_theme_mod( 'tfm_archive_sidebar', true ) || is_page() && get_theme_mod( 'tfm_page_sidebar', false ) ) && is_active_sidebar( 'sidebar-1' ) ) {
			$classes[] = 'has-sidebar';
		}
		if ( is_single( ) && is_active_sidebar( 'sidebar-1' ) && get_theme_mod( 'tfm_single_sidebar', false )) {
			$classes[] = 'sidebar-' . get_theme_mod( 'tfm_single_sidebar_position', 'side' );
		}
		if ( is_page( ) && is_active_sidebar( 'sidebar-1' ) && get_theme_mod( 'tfm_page_sidebar', false )) {
			$classes[] = 'sidebar-' . get_theme_mod( 'tfm_page_sidebar_position', 'side' );
		}
		if ( get_theme_mod( 'tfm_sticky_nav', true ) ) {
			$classes[] = 'has-sticky-nav';
		}
		if ( get_theme_mod( 'tfm_sticky_nav_mobile', true ) ) {
			$classes[] = 'has-sticky-nav-mobile';
		}
		if ( get_theme_mod( 'tfm_header_full_width', false ) ) {
			$classes[] = 'has-full-width-header';
		}
		$classes[] = 'header-' . get_theme_mod( 'tfm_header_layout', 'logo-left-menu-right' );

		$disabled_post_thumbnail = ( is_single() && ( ! get_theme_mod( 'tfm_single_thumbnail', true ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_disable_featured_media', true ) ) ) ) || ( is_page() && ( ! get_theme_mod( 'tfm_page_thumbnail', true ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_disable_featured_media_page', true ) ) ) ) ? true : false;

		$has_featured_media = ( has_post_format('video') && tfm_featured_video( true ) ) || ( has_post_format( 'audio' ) && tfm_featured_audio( true ) ) ? true : false;
		$classes[] = $has_featured_media ? 'has-featured-media' : '';

		if ( is_single() && ( ( has_post_thumbnail() && ! $disabled_post_thumbnail ) || $has_featured_media ) && ( get_theme_mod( 'tfm_single_full_width', false ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_full_width', true ) ) ) ) {
			$classes[] = 'has-full-width-media';
		}
		if ( is_page() && ( ( has_post_thumbnail() && ! $disabled_post_thumbnail ) ) && ( get_theme_mod( 'tfm_page_full_width', false ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_full_width', true ) ) ) ) {
			$classes[] = 'has-full-width-media';
		}
		$single_post_style = function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_post_style', true ) && get_post_meta( get_the_ID(), 'tfm_single_post_style', true ) !== 'global' ? get_post_meta( get_the_ID(), 'tfm_single_post_style', true ) : get_theme_mod( 'tfm_single_post_style', 'default' );
		if ( is_page()) {
			$single_post_style = function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_style', true ) && get_post_meta( get_the_ID(), 'tfm_page_style', true ) !== 'global' ? get_post_meta( get_the_ID(), 'tfm_page_style', true ) : get_theme_mod( 'tfm_page_style', 'default' );
		}
		if ( $has_featured_media && $single_post_style === 'cover' ) {
			$single_post_style = 'default';
		}

		$classes[] = is_single() || is_page() ? 'post-style-' . $single_post_style : '';
		/**
		 *  end
		 */
		if ( get_query_var( 'cpage' ) ) {
			$classes[] = 'comment-page';
		}
		if ( is_single() && ( get_previous_post() || get_next_post() ) && get_theme_mod( 'tfm_single_post_navigation', true) ) {
			$classes[] = 'has-post-nav';
		}
		if ( is_single() && get_theme_mod( 'tfm_single_author_bio', true ) ) {
			$classes[] = 'has-author-bio';
		}
		if ( is_home() && '' !== get_theme_mod( 'tfm_homepage_loop_title', '' ) ) {
			$classes[] = 'has-loop-header';
		}
		if ( is_archive() && get_theme_mod( 'tfm_archive_title_position', 'header' ) === 'loop' && get_theme_mod( 'tfm_archive_title', true ) ) {
			$classes[] = 'header-in-loop';
		}
		if ( get_theme_mod( 'tfm_goto_top', true )) {
			$classes[] = 'has-backtotop';
		}

		// color mode

		$classes[] = 'tfm-' . get_theme_mod( 'tfm_theme_color_scheme', 'system' ) . '-mode';

		// dark/light scheme

		$customizer_settings = tfm_general_settings();

		$classes[] = '' !== get_theme_mod( 'tfm_body_background_color', $customizer_settings['tfm_body_background_color']) ? tfm_is_light_dark( get_theme_mod( 'tfm_body_background_color', $customizer_settings['tfm_body_background_color']) ) : '';
		$classes[] = '' !== get_theme_mod( 'tfm_body_background_color', $customizer_settings['tfm_body_background_color']) && '' !== get_theme_mod( 'tfm_body_background_color_dark', $customizer_settings['tfm_body_background_color_dark'])? 'dark-theme-' . tfm_is_light_dark( get_theme_mod( 'tfm_body_background_color_dark', $customizer_settings['tfm_body_background_color_dark'] ) ) : '';

		return $classes;
	}

}
add_filter( 'body_class', 'tfm_body_classes', 10 );

// ========================================================
// Display the custom logo or title
// ========================================================

if ( ! function_exists( 'tfm_site_logo' ) ) {

	function tfm_site_logo( $args = array() ) {

		$defaults = array(
			'logo'        => '%1$s<span class="screen-reader-text">%2$s</span>',
			'mobile_logo' => '%1$s',
			'footer_logo' => '%1$s',
			'dark_theme_logo' => '%1$s',
			'logo_class'  => 'site-logo',
			'title'       => '<a href="%1$s">%2$s</a>',
			'title_class' => 'site-title',
			'home_wrap'   => '<h1 class="%1$s">%2$s</h1>',
			'single_wrap' => '<div class="%1$s faux-heading">%2$s</div>',
			'footer_wrap' => '<div class="footer-logo">%2$s</div>',
			'mobile'      => false,
			'sidebar'	  => false,
			'footer'      => false,
			'condition'   => ( ( is_front_page() || is_home() )),
		);

		$args = wp_parse_args( $args, $defaults );

		$site_title = get_bloginfo( 'name' );
		$mobile_logo = get_theme_mod( 'tfm_mobile_logo_upload', '' ) ? get_theme_mod( 'tfm_mobile_logo_upload', '' ) : wp_get_attachment_url(get_theme_mod( 'custom_logo' ));
		$sidebar_logo = get_theme_mod( 'tfm_sidebar_logo_upload', '' ) ? get_theme_mod( 'tfm_sidebar_logo_upload', '' ) : wp_get_attachment_url(get_theme_mod( 'custom_logo' ));
		$footer_logo = get_theme_mod( 'tfm_footer_logo_upload', '' ) ? get_theme_mod( 'tfm_footer_logo_upload', '' ) : ''; // display nothing of empty
		// dark theme logos
		$dark_theme_logo = get_theme_mod( 'tfm_logo_upload_dark', '' ) ? get_theme_mod( 'tfm_logo_upload_dark', '' ) : '';
		$sidebar_logo_dark = get_theme_mod( 'tfm_sidebar_logo_upload_dark', '' ) ? get_theme_mod( 'tfm_sidebar_logo_upload_dark', '' ) : '';
		$mobile_logo_dark = get_theme_mod( 'tfm_mobile_logo_upload_dark', '' ) ? get_theme_mod( 'tfm_mobile_logo_upload_dark', '' ) : '';
		$footer_logo_dark = get_theme_mod( 'tfm_footer_logo_upload_dark', '' ) ? get_theme_mod( 'tfm_footer_logo_upload_dark', '' ) : '';

		// logo url
	    $custom_logo_id = $args['mobile'] ? $mobile_logo : wp_get_attachment_url( get_theme_mod( 'custom_logo' ) );
	    if ( $args['sidebar'] ) {
	    	$custom_logo_id = $sidebar_logo;
	    }
	    if ( $args['footer'] ) {
	    	$custom_logo_id = $footer_logo;
	    }
	    $custom_logo_id_dark = $dark_theme_logo;
	    if ( $args['mobile']) {
	    	$custom_logo_id_dark  = $mobile_logo_dark ? $mobile_logo_dark : $dark_theme_logo;
	    }
	    if ( $args['sidebar']) {
	    	$custom_logo_id_dark = $sidebar_logo_dark ? $sidebar_logo_dark : $dark_theme_logo;
	    }
	    if ( $args['footer']) {
	    	$custom_logo_id_dark = $footer_logo_dark;
	    }
	    $logo_size = $args['mobile'] ? wp_get_attachment_image_src( $custom_logo_id, 'full' ) : wp_get_attachment_image_src( get_theme_mod( 'custom_logo' ), 'full' );
	    if ( empty($logo_size)) {
	    	$logo_size = array(1 => false);
	    }
		$logo_width = get_theme_mod( 'tfm_custom_logo_max_width', '210' ) ? get_theme_mod( 'tfm_custom_logo_max_width', '210' ) : $logo_size[1];
		if ( get_theme_mod( 'tfm_retina_logo', false ) ) {
			$logo_width  = floor( $logo_size[1] / 2 );
		}
		if ( $args['sidebar'] ) {
			$logo_width = get_theme_mod( 'tfm_sidebar_custom_logo_max_width', '150' );
		}
		if ( $args['footer'] ) {
			$logo_width = get_theme_mod( 'tfm_footer_custom_logo_max_width', '100' );
		}
		$hidden_dark = $custom_logo_id_dark ? ' hidden-dark-theme' : '';
		$logo = '<a href="' . esc_url( get_home_url() ) . '" rel="home">';
		$logo .= '<img src="' . esc_url( $custom_logo_id ) . '" alt="' . get_bloginfo( 'name' ) . '" class="custom-logo' . $hidden_dark . '" width="' . $logo_width . '" />';
		// Dark theme logos (hidden in light theme)
		if ( $custom_logo_id_dark ) {
			$logo .= '<img src="' . esc_url( $custom_logo_id_dark ) . '" alt="' . get_bloginfo( 'name' ) . '" class="custom-logo dark-theme-logo hidden-light-theme" width="' . $logo_width . '" />';
		}
		$logo .= '</a>';
		$contents   = '';
		$classname  = '';

		if ( $custom_logo_id ) {
			if ( $args['footer']) {
				$contents  = sprintf( $args['footer_logo'], $logo, esc_html( $site_title ) );
				$classname = $args['logo_class'];
			} elseif ( $args['mobile'] ) {
				$contents  = sprintf( $args['mobile_logo'], $logo, esc_html( $site_title ) );
				$classname = $args['logo_class'];
			} else {
				$contents  = sprintf( $args['logo'], $logo, esc_html( $site_title ) );
				$classname = $args['logo_class'];
			}
		} else {
			$contents  = ! $args['footer'] ? sprintf( $args['title'], esc_url( get_home_url( null, '/' ) ), esc_html( $site_title ) ) : '';
			$classname = $args['title_class'];
		}

		$wrap = $args['condition'] && ! $args['mobile'] && ! $args['sidebar'] && ! $args['footer']  ? 'home_wrap' : 'single_wrap';
		if ( $args['footer'] ) {
			$wrap = 'footer_wrap';
		}

		$html = sprintf( $args[ $wrap ], $classname, $contents );

		$html = apply_filters( 'tfm_site_logo', $html, $args, $classname, $contents );

		echo wp_kses_post( $html );

	}

}

// ========================================================
// Output toggle icons
// ========================================================

if ( ! function_exists( 'tfm_toggle_icon' ) ) {

	function tfm_toggle_icon( $icon, $mobile = false ) {

		$mobile_toggle = ( $mobile ? ' mobile-toggle' : '' );
		$has_toggle_background = '';
		$has_toggle_text = ( 'menu' === $icon && get_theme_mod( 'tfm_toggle_menu_text', '' ) || 'search' === $icon && get_theme_mod( 'tfm_toggle_search_text', '' ) ? 'has-toggle-text' : '' );
		$show_icon = ( ( true === $mobile && ( ( $icon === 'menu' && get_theme_mod( 'tfm_toggle_menu_mobile', true ) ) || ( $icon === 'search' && get_theme_mod( 'tfm_toggle_search_mobile', true ) ) || ( $icon === 'color-mode' && get_theme_mod( 'tfm_toggle_color_mode', true ) ) ) ) || 
			// Desktop Menu
			( false === $mobile && ( ( $icon === 'menu' && get_theme_mod( 'tfm_toggle_menu', false ) && ( is_active_sidebar( 'sidebar-2') || has_nav_menu( 'slide-menu-primary' ) ) ) || 
			// Desktop search
			( $icon === 'search' && get_theme_mod( 'tfm_toggle_search', true ) ) ||
			// Desktop color mode
			( $icon === 'color-mode' && get_theme_mod( 'tfm_toggle_color_mode', true ) ) ) ) ? '' : ' hidden' );


		$html = '';

		$html .= '<div class="toggle toggle-' . esc_attr( $icon . $mobile_toggle . $show_icon ) . '">';

		if ( $icon === 'menu' ) {

			$html .= '<span class="screen-reader-text">' . esc_html__( 'Menu', 'jinko' ) . '</span>';

		}

		if ( $icon === 'search' ) {
			
			$html .= '<span class="screen-reader-text">' . esc_html__( 'Search', 'jinko' ) . '</span>';

		}

		if ( $icon === 'color-mode' ) {

		}

		$html .= '</div>';

		echo wp_kses_post( $html );


	}

}


// ========================================================
// Output category tags in post meta
// ========================================================

if ( ! function_exists( 'tfm_get_category_slugs' ) ) {

	function tfm_get_category_slugs( $num = 999, $post_id = false, $html_style = 'li' ) {

		$customizer_settings = tfm_general_settings();

		$num = ( ! is_single() ? get_theme_mod( 'tfm_archive_cat_slug_num', 1 ) : $num );
		$category = array_slice( get_the_category( $post_id ), 0, $num );
		$count = 0;

		$html = '';

		foreach( $category as $the_category ) {

			// Additional color classes
			$slug_color[] = '' !== get_theme_mod( 'category_slug_color_' . $the_category->slug . '', '' ) || '' !== get_theme_mod( 'category_slug_color', $customizer_settings['category_slug_color'] ) ? 'has-slug-color' : '';
			$slug_color[] = '' !== get_theme_mod( 'category_slug_background_' . $the_category->slug . '', '' ) || '' !== get_theme_mod( 'category_slug_background', $customizer_settings['category_slug_background'] ) ? 'has-slug-background' : '';

			$slug_colors = array_filter($slug_color);
			
			$slug_css = count($slug_colors) !== 0 ? ' ' . implode(' ', $slug_color) : '';

			$count++;

			$html.= '<' . $html_style . ' class="cat-slug-' . esc_attr( $the_category->slug ) . ' cat-id-' . esc_attr( $the_category->cat_ID ) . $slug_css . '">';
			// "In" text string
			if ( $count === 1 && tfm_toggle_entry_meta( 'in' ) ) {
				$html .= is_single() ? '<i dir="ltr">' . esc_html__( 'Posted in', 'jinko' ) . '</i> ' : '<i dir="ltr">' . esc_html__( 'in', 'jinko' ) . '</i> ';
			}
			$html .= '<a href="' . get_category_link( $the_category->cat_ID ) . '" class="cat-link-' . esc_attr( $the_category->cat_ID ) . '">' . esc_html( $the_category->cat_name ) . '</a></' . $html_style . '>';

		}

		return $html;

	}

}

// ========================================================
// Output entry meta
// ========================================================

function tfm_get_entry_meta( $meta_data = array(), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = '', $meta_class = '', $cats_wrapper = false, $multi_line = true ) {

	// Make sure we don't have an empty array
	if ( ! $meta_data ) {
		$meta_data = apply_filters( 'tfm_entry_meta_data', array('author_avatar', 'author', 'author_nickname', 'date', 'updated_date', 'comment_count', 'read_time', 'post_views' ) );
	}

	// Create an array of meta items

	/**
	 * Filter empty 
	 * Remove avatar
	 * Set 'multi-meta-items' class
	 * */

	$meta_item['author_avatar'] = in_array('author_avatar', $meta_data ) && tfm_toggle_entry_meta( 'author_avatar' ) ? true : false;
	$meta_item['author'] = in_array('author', $meta_data ) && tfm_toggle_entry_meta( 'author' )  ? true : false;
	$meta_item['author_nickname'] = in_array('author_nickname', $meta_data ) && tfm_toggle_entry_meta( 'author_nickname' )  ? true : false;
	$meta_item['date'] = in_array('date', $meta_data ) && tfm_toggle_entry_meta( 'date' ) ? true : false;
	$meta_item['updated_date'] = in_array('updated_date', $meta_data ) && tfm_toggle_entry_meta( 'updated_date' ) ? true : false;
	$meta_item['comment_count'] = in_array('comment_count', $meta_data ) && tfm_toggle_entry_meta( 'comment_count' ) ? true : false;
	$meta_item['read_time'] = in_array('read_time', $meta_data ) && tfm_toggle_entry_meta( 'read_time' ) ? true : false;
	$meta_item['category'] = in_array('category', $meta_data ) && tfm_toggle_entry_meta( 'category' )  ? true : false;
	$meta_item['post_views'] = in_array('post_views', $meta_data) && function_exists( 'tfm_post_views') && '' !== tfm_post_views($post_id = '', $precision = 1, $abbrv = true, $views_style = $html_style, $return = true ) ? true : false;

	$meta_items = array_filter($meta_item);

	unset($meta_items['author_avatar']); // do not count avatar

	$count_meta_items = count($meta_items);

	$meta_items_class = $count_meta_items > 1 ? ' multi-meta-items' : ''; // more than one meta item (not including avatar)

	// Additonal classes
	$meta_wrapper_class = '' === $meta_wrapper_class ? ' after-title' : ' ' . $meta_wrapper_class;
	$meta_class = '' !== $meta_class ? ' ' . $meta_class : '';
	$meta_class .= $meta_item['author_avatar'] ? ' has-avatar' : '';
	$container_style = $html_style === 'li' ? 'ul' : 'div';
	$multi_line_class = $multi_line && $meta_item['author_avatar'] && ($meta_item['author'] || $meta_item['author_nickname']) && $meta_items_class ? ' multi-line' : '';
	$multi_line_class .= $multi_line_class && ! is_single() && ( get_theme_mod('tfm_mobile_hide_entry_meta_author_avatar', false ) || get_theme_mod('tfm_mobile_hide_entry_meta_author', false ) ) ? ' single-line-mobile' : '';

	// Hidden mobile
	$hidden_avatar = ! is_single() && $meta_item['author_avatar'] && get_theme_mod('tfm_mobile_hide_entry_meta_author_avatar', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_author = ! is_single() && $meta_item['author'] && get_theme_mod('tfm_mobile_hide_entry_meta_author', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_author_nickname = ! is_single() && $meta_item['author_nickname'] && get_theme_mod('tfm_mobile_hide_entry_meta_author_nickname', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_date = ! is_single() && $meta_item['date'] && get_theme_mod('tfm_mobile_hide_entry_meta_date', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_comment_count = ! is_single() && $meta_item['comment_count'] && get_theme_mod('tfm_mobile_hide_entry_meta_comment_count', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_read_time = ! is_single() && $meta_item['read_time'] && get_theme_mod('tfm_mobile_hide_entry_meta_read_time', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_category = ! is_single() && $meta_item['category'] && get_theme_mod('tfm_mobile_hide_entry_meta_category', false ) ? ' hidden-mobile' : ' visible-mobile';
	$hidden_posts_views = ! is_single() && ! is_page() && $meta_item['post_views'] && get_theme_mod('tfm_mobile_hide_entry_meta_post_views', false ) ? ' hidden-mobile' : ' visible-mobile';

	// Hide the entire wrapper if no meta items
	
	$wrapper_items = array_filter($meta_item);
	$hidden_status = array();
	foreach ($wrapper_items as $item => $value) {
		$hidden_status[$item] = get_theme_mod( 'tfm_mobile_hide_entry_meta_' . $item, false );
	}

	$hidden_items = array_filter($hidden_status);

	$hidden_entry_meta_wrapper = ( ! is_single() && ! is_page() ) && count($hidden_items) === count($wrapper_items) ? ' hidden-mobile' : '';

	$html = '';

	if ( ! empty($meta_items) || $meta_item['author_avatar'] || $meta_item['category'] ) :

		if ( $has_wrapper ) {

			$html .= '<div class="entry-meta' . $meta_wrapper_class . $meta_items_class . $multi_line_class . $hidden_entry_meta_wrapper . '">';

		}

		if ( $has_container ) {

			$html .= '<' . $container_style . ' class="post-meta' . $meta_class . $multi_line_class . '">';

		}

	// Avatar

	if ( $meta_item['author_avatar'] ) :

		$avatar_size = is_single() ? 60 : 60;

		$has_custom_avatar_size = '';

		if ( array_key_exists('args', $meta_data ) && array_key_exists('avatar_size', $meta_data['args']) ) {

			$avatar_size = $meta_data['args']['avatar_size'];
			$has_custom_avatar_size = ' has-custom-avatar-size';

		}

		$html .= '<' . $html_style . ' class="entry-meta-avatar' . $hidden_avatar . $has_custom_avatar_size . '">';

		$html .= '<a href="' . get_author_posts_url( get_the_author_meta( 'ID' ), get_the_author_meta( 'user_nicename' ) ) . '">';

		$html .= get_avatar( get_the_author_meta('ID'), esc_attr( $avatar_size ) );

		$html .= '</a>';

		$html .= '</' . $html_style . '>';


	endif;

	// Author

	if ( $meta_item['author'] ) :

		$html .= '<' . $html_style . ' class="entry-meta-author' . $hidden_author . '">';

		$html .= '<span class="screen-reader-text">' . esc_html__( 'Posted by', 'jinko' ) . '</span>';
		if ( tfm_toggle_entry_meta( 'by' ) ):
			$html .= '<i dir="ltr">' . esc_html__( 'by', 'jinko' ) . '</i> ';
			endif;
		$html .= '<a href="' . get_author_posts_url( get_the_author_meta( 'ID' ), get_the_author_meta( 'user_nicename' ) ) . '">' . get_the_author() . '</a>';

		$html .= '</' . $html_style . '>';

	endif;

	if ( $meta_item['author_nickname'] ) :

			$html .= '</' . $html_style . '>';

			$html .= '<' . $html_style . ' class="entry-meta-author-nickname' . $hidden_author_nickname . '">';

			$html .= '<span class="nickname">' . get_the_author_meta( 'nickname' ) . '</span>';


		$html .= '</' . $html_style . '>';

	endif;

	// Date

	if ( $meta_item['date']) :

		$tfm_date = function_exists('tfm_human_entry_date') ? tfm_human_entry_date() : get_the_time( get_option( 'date_format' ));

		$html .= '<' . $html_style . ' class="entry-meta-date' . $hidden_date . '">';

		$title = get_the_title('','', false);

		if ( ! is_single( ) && strlen($title) == 0 ) {

		$html .= '<a href="' . get_the_permalink() . '">';

		}

		$html .= '<time datetime="' . get_the_date( 'Y-m-d' ) . '">' . $tfm_date . '</time>';

		if ( ! is_single( ) && strlen($title) == 0 ) {

		$html .= '</a>';

		}

	   $html .= '</' . $html_style . '>';

	endif;

	// Updated date

	if ( $meta_item['updated_date'] ) :

		$html .= '<' . $html_style . ' class="entry-meta-date-updated">';

		$html .= tfm_updated_date();

	    $html .= '</' . $html_style . '>';

	endif;

	// Comment count

	if ( $meta_item['comment_count']) :

		$html .= '<' . $html_style . ' class="entry-meta-comment-count' . $hidden_comment_count . '">';

		if ( is_single( ) ) : 

			$html .= '<a href="#comments">';

		endif;

		$comment_string = (int)get_comments_number() === 1 ? esc_html__( ' Comment', 'jinko' ) : esc_html__( ' Comments', 'jinko' );

		$html .= get_comments_number() . '<span>' . esc_attr( $comment_string ) . '</span>';

		if ( is_single( ) ) :

			$html .= '</a>';

		endif;

	$html .= '</' . $html_style . '>';

	endif;

	// TFM Read time (theme boost plugin)

	if ( function_exists('tfm_read_time') ) :

		if ( $meta_item['read_time'] && tfm_show_read_time() ) :

			$html .= tfm_read_time( $forced_request = true, $return = true, $style = $html_style, $classes = $hidden_read_time  );

		endif;

	endif;

	// TFM Post Views (theme boost plugin)

	if ( function_exists('tfm_post_views') ) :

		if ( $meta_item['post_views']) :

			$html .= tfm_post_views( $post_id = '', $precision = 1, $abbrv = true, $html_style = $html_style, $return = true, $forced_request = false, $hidden_posts_views );

		endif;

	endif;

	if ( $meta_item['category'] ) :

		$num = 999;
		$post_id = false;

			if ( array_key_exists('args', $meta_data ) ) {

				$num = array_key_exists('num', $meta_data['args']) ? $meta_data['args']['num'] : '';
				$post_id = array_key_exists('post_id', $meta_data['args']) ? $meta_data['args']['post_id'] : false;
			}

		$html .= $cats_wrapper ? '<li class="entry-meta-categories"><ul>' : '';

		$html .= tfm_get_category_slugs( $num, $post_id, $html_style );

		$html .= $cats_wrapper ? '</li></ul>' : '';

	endif;

	if ( $has_container ) {

		$html .= '</' . $container_style . '>';

	}

	if ( $has_wrapper ) {

		$html .= '</div>';

	}

	endif; // endif after/before meta

	echo wp_kses_post( $html );

}

// ========================================================
// Additional custom post_class classes
// ========================================================

if ( ! function_exists( 'tfm_post_class' ) ) {

	function tfm_post_class( $classes ) {

		global $post;

		$customizer_settings = tfm_general_settings();

		$classes[] = 'article'; // Always set .article class

		// Post background class

		$default_post_background = array_key_exists('default_post_background', $customizer_settings ) ? $customizer_settings['default_post_background'] : '';
		$default_post_background_dark = array_key_exists('default_post_background_dark', $customizer_settings ) ? $customizer_settings['default_post_background_dark'] : '';

		$tfm_post_background = '' !== get_theme_mod( 'tfm_post_background', '' ) ? get_theme_mod( 'tfm_post_background', '' ) : $default_post_background;
		$tfm_post_background_dark = '' !== get_theme_mod( 'tfm_post_background_dark', '' ) ? get_theme_mod( 'tfm_post_background_dark', '' ) : $default_post_background_dark;
		
		if ( '' !== $tfm_post_background && get_theme_mod( 'tfm_remove_post_background', false)) {
			$tfm_post_background = '';
		}
		$card_style = is_home() ? get_theme_mod( 'tfm_homepage_loop_style', 'card' ) : get_theme_mod( 'tfm_archive_loop_style', 'card' );

		if ( 'card' !== $card_style ) {
			$tfm_post_background = '';
		}

		if ( ! is_single() && ! is_page() && '' !== $tfm_post_background ) {
			$classes[] = 'has-background';
		}
		if ( is_single() && '' !== get_theme_mod( 'tfm_post_background_single', '' )) {
			$classes[] = 'has-background';
		}

		// if we have a post background check dark/light

		$classes[] = '' !== $tfm_post_background ? tfm_is_light_dark( get_theme_mod( 'tfm_post_background', '' ) ) : '';
		$classes[] = '' !== $tfm_post_background && '' !== $tfm_post_background_dark ? 'dark-theme-' . tfm_is_light_dark( get_theme_mod( 'tfm_post_background_dark', '' ) ) : '';

		//  Meta
		if ( tfm_toggle_entry_meta( 'excerpt' ) ) {
			$classes[] = 'has-excerpt';
		}
		if ( tfm_toggle_entry_meta( 'author_avatar' ) ) {
			$classes[] = 'has-avatar';
		}
		if ( tfm_toggle_entry_meta( 'author' ) ) {
			$classes[] = 'has-author';
		}
		if ( tfm_toggle_entry_meta( 'author_nickname' ) ) {
			$classes[] = 'has-nickname';
		}
		if ( tfm_toggle_entry_meta( 'date' ) || tfm_toggle_entry_meta( 'updated_date' ) ) {
			$classes[] = 'has-date';
		}
		if ( tfm_toggle_entry_meta( 'comment_count' ) ) {
			$classes[] = 'has-comment-count';
		}
		if ( tfm_toggle_entry_meta( 'category' ) ) {
			$classes[] = 'has-category-meta';
		}
		// Read more
		if ( tfm_toggle_entry_meta( 'read_more' ) ) {
				$classes[] = 'has-read-more';
		}
		if ( tfm_toggle_entry_meta( 'entry_title' ) ) {
				$classes[] = 'has-title';
		}
		// post format icons
		if ( ! is_single() && get_theme_mod( 'tfm_post_format_icons', true ) && ( has_post_format( 'video') || has_post_format( 'audio') || has_post_format( 'gallery') || has_post_format( 'image') ) ) {
			$classes[] = 'has-format-icons';
		}


		// Disabled Post Thumbnail
		$disabled_thumbnail = false;

		if ( ( is_archive() || is_search() ) && ! get_theme_mod( 'tfm_archive_post_thumbnail', true ) ||
		is_home() && ! get_theme_mod( 'tfm_homepage_post_thumbnail', true ) ||
		is_single() && ( ! get_theme_mod( 'tfm_single_thumbnail', true ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_disable_featured_media', true ) ) ) || is_page() && ( ! get_theme_mod( 'tfm_page_thumbnail', true ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_disable_featured_media_page', true ) ) ) )  {

				$classes[] = 'disabled-post-thumbnail';
				$disabled_thumbnail = true;

		}

		$show_media = is_home() ? get_theme_mod( 'tfm_homepage_post_media', true ) : get_theme_mod( 'tfm_archive_post_media', true );

		if ( ( ( has_post_format( 'video') && tfm_featured_video( true ) ) || ( has_post_format( 'audio') && tfm_featured_audio( true ) ) ) || ( has_post_thumbnail( ) && ! $disabled_thumbnail ) ) {
			$classes[] = 'has-post-media';
		}

		// Media files
		if ( $show_media && ( has_post_format( 'video') && tfm_featured_video( true ) ) || ( has_post_format( 'audio') && tfm_featured_audio( true ) ) ) {
			$classes[] = 'has-post-format-media';
		}

		// Thumbnail size
		$thumbnail_size = 'thumbnail-';

			if ( is_single() ) {
				if ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_post_thumbnail_size', true ) && get_post_meta( get_the_ID(), 'tfm_single_post_thumbnail_size', true ) !== 'global' ) {
				$thumbnail_size = $thumbnail_size . get_post_meta( get_the_ID(), 'tfm_single_post_thumbnail_size', true );
				} else {
					$thumbnail_size = $thumbnail_size . get_theme_mod( 'tfm_single_thumbnail_aspect_ratio', 'hero' );
				}
			}
			if ( is_page() ) {
				if ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_thumbnail_size', true ) && get_post_meta( get_the_ID(), 'tfm_page_thumbnail_size', true ) !== 'global' ) {
				$thumbnail_size = $thumbnail_size . get_post_meta( get_the_ID(), 'tfm_page_thumbnail_size', true );
				} else {
					$thumbnail_size = $thumbnail_size . get_theme_mod( 'tfm_page_thumbnail_aspect_ratio', 'hero' );
				}
			}
			if ( is_home() ) {
				$thumbnail_size = $thumbnail_size . get_theme_mod( 'tfm_homepage_thumbnail_aspect_ratio', 'uncropped' );
			}
			if ( is_archive() || is_search() ) {
				$thumbnail_size =  $thumbnail_size . get_theme_mod( 'tfm_archive_thumbnail_aspect_ratio', 'landscape' );
			}

		$classes[] = $thumbnail_size;

		// Post style
		if ( is_home() || is_archive() || is_search() ) {

			$archive_style = is_home() ? get_theme_mod( 'tfm_homepage_loop_style', 'card' ) : get_theme_mod( 'tfm_archive_loop_style', 'card' );

			if ( 'list' === tfm_get_post_layout() || 'list-grid' === tfm_get_post_layout() ) {
				$classes[] = 'default';
			} else {

				if ( $show_media && ( ( has_post_format( 'video' ) && tfm_featured_video( true ) ) || ( has_post_format( 'audio') && tfm_featured_audio( true ) ) ) ) {
					$classes[] = 'cover' !== $archive_style ? $archive_style : 'default';
				} elseif ( has_post_format( 'image' ) && has_post_thumbnail() && ! $disabled_thumbnail) {
					$classes[] = 'list' !== tfm_get_post_layout() && 'list-grid' !== tfm_get_post_layout()  ? 'cover' : '';
				} else {
					$classes[] = $archive_style;
				}

			}
		} 
		
		// Override post style for single and page (check for custom post meta)
		if ( is_single() ) {
			$single_post_style = function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_post_style', true ) && get_post_meta( get_the_ID(), 'tfm_single_post_style', true ) !== 'global' ? get_post_meta( get_the_ID(), 'tfm_single_post_style', true ) : get_theme_mod( 'tfm_single_post_style', 'default' );
			if ( ( ! has_post_thumbnail() || $disabled_thumbnail || has_post_format('video') || has_post_format( 'audio' ) ) && $single_post_style === 'cover' ) {
				$single_post_style = 'default';
			}
			$classes[] = $single_post_style;
		}
		if ( is_page() ) {
			$single_page_style = function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_style', true ) && get_post_meta( get_the_ID(), 'tfm_page_style', true ) !== 'global' ? get_post_meta( get_the_ID(), 'tfm_page_style', true ) : get_theme_mod( 'tfm_page_style', 'default' );
			if ( ( ! has_post_thumbnail() || $disabled_thumbnail ) && $single_page_style === 'cover' ) {
				$single_page_style = 'default';
			}
			$classes[] = $single_page_style;
		}

			return $classes;

	}

	add_filter( 'post_class', 'tfm_post_class' );

}

// ========================================================
// Get post cols count
// ========================================================

if ( ! function_exists('tfm_get_post_cols') ) {

	function tfm_get_post_cols( $value = 'class', $page = '' ) {

		$post_layout = tfm_get_post_layout();
		$post_cols = ( is_home() ? get_theme_mod( 'tfm_homepage_loop_cols', 3 ) : get_theme_mod( 'tfm_archive_loop_cols', 3 ) );
		if ( is_single() || is_page() ) {
			$post_cols = false;
		}
		// page query
		if ( '' !== $page && $page === 'archive') {
			$post_cols = get_theme_mod( 'tfm_archive_loop_cols', 3 );
		}
		if ( '' !== $page && $page === 'home' ) {
			$post_cols = get_theme_mod( 'tfm_homepage_loop_cols', 3 );
		}
		$enabled_sidebar = ( is_home() ? get_theme_mod( 'tfm_homepage_sidebar', true ) : get_theme_mod( 'tfm_archive_sidebar', true ) );
		$has_sidebar = is_active_sidebar( 'sidebar-1') && $enabled_sidebar ? true : false;

		$max_post_cols = $post_layout === 'list' ? apply_filters( 'tfm_max_post_cols_list', 1 ) : apply_filters( 'tfm_max_post_cols_grid', 4 );
		$max_post_cols_width_sidebar = $post_layout === 'list' ? apply_filters( 'tfm_max_post_cols_list_width_sidebar', 1 ) : apply_filters( 'tfm_max_post_cols_with_sidebar', 3 );

		// Check for max columns filters (per theme basis)
		if ( $post_cols > $max_post_cols ) {
			$post_cols = $max_post_cols;
		}
		
		// Check for sidebar and adjust cols
		if ( $has_sidebar && $post_cols > $max_post_cols_width_sidebar ) {
			$post_cols = $max_post_cols_width_sidebar;

		}

		// empty search results
		 if ( is_search()) {
		    global $wp_query;
		    $post_cols = 0 === $wp_query->found_posts ? 1 : $post_cols;
		 }

		if ( '' === $value || 'class' === $value ) {
			$post_cols = ' cols-' . $post_cols;
		}

		if ( 'count' === $value ) {
			$post_cols = $post_cols;
		}

		return $post_cols;
	}

}

// ========================================================
// Get post layout
// ========================================================

if ( ! function_exists('tfm_get_post_layout') ) {

	function tfm_get_post_layout( $mobile = false ) {

		if ( is_single() || is_page()) {
			return;
		}

		$post_layout = is_home() ? get_theme_mod( 'tfm_homepage_layout', 'masonry' ) : get_theme_mod( 'tfm_archive_layout', 'list' );
		$post_style = is_home() ? get_theme_mod( 'tfm_homepage_loop_style', 'card' ) : get_theme_mod( 'tfm_archive_loop_style', 'card' );
		if ( $mobile ) {
			$post_layout .= $post_layout !== 'masonry' && $post_style !== 'cover' ? ' mobile-' . get_theme_mod( 'tfm_mobile_layout', 'grid' ) : '';
		}

		return $post_layout;
	}

}


// ========================================================
// Output the exerpt
// ========================================================

if ( ! function_exists( 'tfm_get_excerpt' ) ) {

	function tfm_get_excerpt( $length = '', $post_id = '', $hide_excerpt = false, $forced = false ) {

		$excerpt_length = $length ? $length : get_theme_mod( 'tfm_excerpt_length', 30 );
		if ( has_excerpt( $post_id) ) {
			$excerpt_length = 999; // Show full custom excerpt
		}

		$html = '';

		$hidden_mobile = ( ! is_single() && get_theme_mod( 'tfm_mobile_hide_excerpt', false ) ) || $hide_excerpt ? ' hidden-mobile' : '';

		if ( tfm_toggle_entry_meta( 'excerpt' ) || $post_id || $forced ) {

			$html .= '<div class="entry-content excerpt' . $hidden_mobile . '">';
			$html .= wp_trim_words(get_the_excerpt( $post_id ), $excerpt_length );
			$html .= '</div>';

		}

		echo wp_kses_post( $html );

	}

}

// ========================================================
// Modify Excerpt more
// ========================================================

if ( ! function_exists( 'tfm_excerpt_more' ) ) {

	function tfm_excerpt_more( $more ) {

	return '...';

	}

}

add_filter( 'excerpt_more', 'tfm_excerpt_more' );

// ========================================================
// Toggle entry-meta displays
// ========================================================

/**
 * This function handles meta data displays
 */

if ( ! function_exists( 'tfm_toggle_entry_meta' ) ) {

	function tfm_toggle_entry_meta( $meta_data = '' ) {

		$show_meta = true;

		// Category
		if ( $meta_data == 'category' ) {

			if ( ( is_home() && 
				   ! get_theme_mod( 'tfm_homepage_entry_meta_category', true ) ) || 
				   ( ( is_archive() || is_search() ) && 
				   ! get_theme_mod( 'tfm_archive_entry_meta_category', true ) ) ||
				   is_single() && ! get_theme_mod( 'tfm_single_entry_meta_category', true ) ) {

				$show_meta = false;

			}

			if ( ! has_category( )) {
				$show_meta = false;
			}

		}


		// by
		if ( $meta_data == 'by' ) {

			if ( ( is_home() && 
				   ! get_theme_mod( 'tfm_homepage_entry_meta_by', true ) ) || 
				   ( ( is_archive() || is_search() ) && 
				   ! get_theme_mod( 'tfm_archive_entry_meta_by', true ) ) ||
				   ( is_single() && 
				   ! get_theme_mod( 'tfm_single_entry_meta_by', true ) ) ) {

				$show_meta = false;

			}

		}

		// in
		if ( $meta_data == 'in' ) {

			if ( ( is_home() && 
				   ! get_theme_mod( 'tfm_homepage_entry_meta_in', false ) ) || 
				   ( ( is_archive() || is_search() ) && 
				   ! get_theme_mod( 'tfm_archive_entry_meta_in', false ) ) ||
				   ( is_single() && 
				   ! get_theme_mod( 'tfm_single_entry_meta_in', false ) ) ) {

				$show_meta = false;

			}

		}

		// Author
		if ( $meta_data == 'author' ) {

			if ( ( is_home() && 
				   ! get_theme_mod( 'tfm_homepage_entry_meta_author', true ) ) || 
				   ( ( is_archive() || is_search() ) && 
				   ! get_theme_mod( 'tfm_archive_entry_meta_author', true ) ) ||
				   ( is_single() && 
				   ! get_theme_mod( 'tfm_single_entry_meta_author', true ) ) ) {

				$show_meta = false;

			}

		}

		// Author avatar
		if ( $meta_data == 'author_avatar' ) {

			if ( ( is_home() && 
				   ! get_theme_mod( 'tfm_homepage_entry_meta_author_avatar', true ) ) || 
				   ( ( is_archive() || is_search() ) && 
				   ! get_theme_mod( 'tfm_archive_entry_meta_author_avatar', true ) ) ||
				   ( is_single() && 
				   ! get_theme_mod( 'tfm_single_entry_meta_author_avatar', true ) ) ) {

				$show_meta = false;

			}

		}

		// Author nickname
		if ( $meta_data == 'author_nickname' ) {

			if ( ( is_home() && 
				   ! get_theme_mod( 'tfm_homepage_entry_meta_author_nickname', false ) ) || 
				   ( ( is_archive() || is_search() ) && 
				   ! get_theme_mod( 'tfm_archive_entry_meta_author_nickname', false ) ) ||
				   ( is_single() && 
				   ! get_theme_mod( 'tfm_single_entry_meta_author_nickname', false ) ) ||
				   '' === get_the_author_meta( 'nickname' ) ) {

				$show_meta = false;

			}

		}

		// Date
		if ( $meta_data == 'date' ) {

			if ( ( is_home() && 
			       ! get_theme_mod( 'tfm_homepage_entry_meta_date', true ) ) ||
			       // Archive
			       ( ( is_archive() || is_search() ) && ! get_theme_mod( 'tfm_archive_entry_meta_date', true ) ) ||
			       // Single
			       ( is_single() && ! get_theme_mod( 'tfm_single_entry_meta_date', true ) ) ) {

				$show_meta = false;

			}

		}

		// Date Updated
		if ( $meta_data == 'updated_date' ) {

			$u_time = get_the_time('U'); 
			$u_modified_time = get_the_modified_time('U'); 

			$show_updated = ( ( $u_modified_time >= $u_time + 86400 ) ? true : false );

			if ( ! is_single() || ( is_single() && ! get_theme_mod( 'tfm_single_entry_meta_date_updated', false ) ) ) {

				$show_meta = false;

			}

			if ( $show_meta && false === $show_updated && get_theme_mod( 'tfm_single_entry_meta_date', true ) ) {

				$show_meta = false;

			}

		}

		// Comment Count
		if ( $meta_data == 'comment_count' ) {

			if ( ( is_home() && 
			       ! get_theme_mod( 'tfm_homepage_entry_meta_comment_count', true ) ) || 
			       ( ( is_archive() || is_search() ) && 
			       ! get_theme_mod( 'tfm_archive_entry_meta_comment_count', true ) ) ||
			       ( is_single() && 
			       ! get_theme_mod( 'tfm_single_entry_meta_comment_count', true ) ) ) {

				$show_meta = false;

			}

		}

		// Comment Count
		if ( $meta_data == 'excerpt' ) {

			if ( ( is_home() && 
			       ! get_theme_mod( 'tfm_homepage_post_excerpt', true ) ) || 
			       ( ( is_archive() || is_search() ) && 
			       ! get_theme_mod( 'tfm_archive_post_excerpt', true ) ) ||
			       ( is_single() && 
			       ( ! get_theme_mod( 'tfm_single_custom_excerpt', false ) || ( get_theme_mod( 'tfm_single_custom_excerpt', false ) && ! has_excerpt( ) ) ) ) ) {

				$show_meta = false;

			}

		}

		// Read more
		if ( $meta_data == 'read_more' ) {

			if ( is_single() || ( is_home() && 
			       ! get_theme_mod( 'tfm_homepage_read_more', false ) ) || 
			       ( ( is_archive() || is_search() ) && 
			       ! get_theme_mod( 'tfm_archive_read_more', false ) ) ) {

				$show_meta = false;

			}

		}

		// Read more
		if ( $meta_data == 'entry_title' ) {

			if ( (( is_archive() || is_search() ) && 
			       ! get_theme_mod( 'tfm_archive_entry_title', true ) ) || ( is_home() && ! get_theme_mod( 'tfm_homepage_entry_title', true ) ) ) {

				$show_meta = false;

			}

		}

		// Read time (tfm theme boost)
		if ( $meta_data == 'read_time' ) {

			if ( ( is_single() && ! get_theme_mod( 'tfm_single_entry_meta_read_time', true )) || ( is_home() && 
			       ! get_theme_mod( 'tfm_homepage_entry_meta_read_time', true ) ) || 
			       ( ( is_archive() || is_search() ) && 
			       ! get_theme_mod( 'tfm_archive_entry_meta_read_time', true ) ) ) {

				$show_meta = false;

			}

		}

		// post views (tfm theme boost)
		if ( $meta_data == 'post_views' ) {

			if ( ! function_exists( 'tfm_post_views') ||
			(function_exists( 'tfm_post_views') && '' === tfm_post_views($post_id = '', $precision = 1, $abbrv = true, $views_style = '', $return = true )) ) {

				$show_meta = false;

			}

		}

		return $show_meta;

	}

}

if ( ! function_exists('tfm_updated_date') ) {

	function tfm_updated_date( ) {

		$html = '';

		$u_time = get_the_time('U'); 
		$u_modified_time = get_the_modified_time('U'); 

		$show_updated = ( ( $u_modified_time >= $u_time + 86400 ) ? true : false );

		if ( $show_updated ) {
			if ( get_theme_mod('tfm_human_entry_date', false ) ) {
				$html .= '<span>' . esc_html__( 'Updated:', 'jinko' ) . '</span> ';
			} else {
				$html .= '<span>' . esc_html__( 'Updated:', 'jinko' ) . '</span> ';
			}
		}
		$html .= '<time datetime="' . get_the_modified_date( 'Y-m-d' ) . '">' . get_the_modified_date( ) . '</time>';

		return $html;

	}
}

// ========================================================
// Full width featured media (single & page)
// ========================================================

// simply returns a true/false 

if ( ! function_exists( 'tfm_full_width_featured_media') ) {

	function tfm_full_width_featured_media( $echo_value = false ) {

		$full_width = false;

		if ( is_single() && ( get_theme_mod( 'tfm_single_full_width', false ) || ( false === get_theme_mod( 'tfm_single_full_width', false ) && function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_full_width', true ) ) ) ) {
			$full_width = true;
		}

		if ( is_page() && ( get_theme_mod( 'tfm_page_full_width', false ) || ( false === get_theme_mod( 'tfm_page_full_width', false ) && function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_full_width', true ) ) ) ) {
			$full_width = true;
		}

		// return false if we have a side sidebar

		if ( in_array( 'sidebar-side', get_body_class( )) ) {

			$full_width = false;

		}

		if ( $echo_value ) {

			$full_width = $full_width ? 'true' : 'false';

		}

			return $full_width;



	}

}

// ========================================================
// Get the post thumbnail size
// ========================================================

if ( ! function_exists( 'tfm_get_post_thumbnail') ) {

	function tfm_get_post_thumbnail( $thumb_size = '', $aspect_ratio = '', $count = 0 ) {

		global $post;

		$site_settings = tfm_general_settings();
		$large_size_w = get_option('large_size_w');
		$site_width = get_theme_mod( 'tfm_site_width', $site_settings['site_width'] ) <= $site_settings['site_width'] ? $site_settings['site_width'] : get_theme_mod( 'tfm_site_width', $site_settings['site_width'] );


		// Post Columns (posts per row)
		$post_cols = tfm_get_post_cols( 'count' );
		// Layout
		$post_layout = tfm_get_post_layout();
		// Post style
		$post_style = is_home() ? get_theme_mod( 'tfm_homepage_loop_style', 'card' ) : get_theme_mod( 'tfm_archive_loop_style', 'card' );

		// ========================================================
		// Define the image sizes
		// ========================================================

		$size = '' === $thumb_size ? apply_filters( 'tfm_archive_medium_thumbnail_size', 'medium_large' ) : $thumb_size;

		// Check for small thumbnail
		$thumbnail  = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'tfm-small-image' );
		$thumbnail_src = $thumbnail ? $thumbnail[0] : '';
		$thumbnail_check = strpos($thumbnail_src, ''. apply_filters( 'tfm_small_image_width', 440 ) . '');

		// Check for single thumbnail size
		$single_thumbnail = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'tfm-single-image' );
		$single_thumbnail_src = $single_thumbnail ? $single_thumbnail[0] : '';
		$check_single_thumbnail = strpos($single_thumbnail_src, apply_filters( 'tfm_single_image_width', '' . $site_width . '' ));

		// Check for mobile thumbnail
		$mobile_thumbnail  = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'tfm-mobile-image' );
		$mobile_thumbnail_src = $mobile_thumbnail ? $mobile_thumbnail[0] : '';
		$mobile_thumbnail_check = strpos($mobile_thumbnail_src, ''. apply_filters( 'tfm_mobile_image_width', 320 ) . '');


		// Single and full width archive thumbnail size
		$single_size = $check_single_thumbnail ? 'tfm-single-image' : apply_filters( 'tfm_single_thumbnail_size', 'full' );
		$large_archive_thumbnail_size = $check_single_thumbnail ? 'tfm-single-image' : apply_filters( 'tfm_archive_large_thumbnail_size', 'large' );

		// tfm-small-image fallback
		if ( $thumb_size === 'tfm-small-image' ) {
			$size = $thumbnail_check ? 'tfm-small-image' : 'medium_large';
		}
		if ( $thumb_size === 'tfm-mobile-image' ) {
			$size = $mobile_thumbnail_check ? 'tfm-mobile-image' : 'medium';
		}

		// ========================================================
		// No tfm-single-image 
		// ========================================================

		/**
		 * Let's try something else before we display the full image
		 * Settings > Media
		 * If WP Large image is same or more than site width use large
		 */

		if ( ! $check_single_thumbnail && $large_size_w >= apply_filters( 'tfm_single_image_width', $site_width )) {
			$single_size = apply_filters( 'tfm_single_large_thumbnail_size', 'large' );
		}

		// Default mobile image
		$mobile_size = $mobile_thumbnail_check ? 'tfm-mobile-image' : 'medium';

		if ( '' === $thumb_size ) {

		// Default archive thumbnail size 3-4 columns
		$size = $thumbnail_check ? 'tfm-small-image' : $size;

		// 1 Column
		if ( $post_cols === 1 ) {

			$size = $large_archive_thumbnail_size;

			// check max width setting if less than site width use smaller image
			if ( ( get_theme_mod( 'tfm_no_sidebar_blog_max_width', $site_settings['no_sidebar_blog_max_width']) < get_theme_mod( 'tfm_site_width', $site_settings['site_width'] ) ) && get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols1', true ) ) {
				$size = apply_filters( 'tfm_single_large_thumbnail_size', 'large' );
			}

		}

		// 2 Column
		if ( $post_cols === 2 ) {

			$size = apply_filters( 'tfm_archive_medium_thumbnail_size', 'medium_large' );

			if ( get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols2', false ) && get_theme_mod( 'tfm_no_sidebar_blog_max_width', $site_settings['no_sidebar_blog_max_width']) < get_theme_mod( 'tfm_site_width', $site_settings['site_width'] ) ) {
				$size = $thumbnail_check ? 'tfm-small-image' : $size;
			}

			// check max width setting if full width and portrait
			if ( ( ( get_theme_mod( 'tfm_no_sidebar_blog_max_width', $site_settings['no_sidebar_blog_max_width']) >= get_theme_mod( 'tfm_site_width', $site_settings['site_width'] ) ) && get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols2', false ) ) || ! get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols2', false) && $aspect_ratio === 'portrait' ) {
				$size = apply_filters( 'tfm_single_large_thumbnail_size', 'large' );
			}

		}

		// 3 Column
		if ( $post_cols === 3 ) {

			//$size = apply_filters( 'tfm_archive_small_thumbnail_size', 'tfm-small-image' );

			// check max width setting if full width and portrait
			if ( ( ( get_theme_mod( 'tfm_no_sidebar_blog_max_width', $site_settings['no_sidebar_blog_max_width']) >= get_theme_mod( 'tfm_site_width', $site_settings['site_width'] ) ) && get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols3', false ) ) || ! get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols3', false) && $aspect_ratio === 'portrait' ) {
				$size = apply_filters( 'tfm_archive_medium_thumbnail_size', 'medium_large' );
			}

		}

		// 4 Column
		if ( $post_cols === 4 ) {

			//$size = apply_filters( 'tfm_archive_xsmall_thumbnail_size', 'tfm-small-image' );

			// check max width setting if full width and portrait
			if ( ( ( get_theme_mod( 'tfm_no_sidebar_blog_max_width', $site_settings['no_sidebar_blog_max_width']) >= get_theme_mod( 'tfm_site_width', $site_settings['site_width'] ) ) && get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols4', false ) ) || ! get_theme_mod( 'tfm_no_sidebar_blog_max_width_cols4', false) && $aspect_ratio === 'portrait' ) {
				$size = apply_filters( 'tfm_archive_medium_thumbnail_size', 'medium_large' );
			}

		}

		// List
		if ( $post_layout === 'list' ) {

			$size = apply_filters( 'tfm_archive_medium_thumbnail_size', 'medium_large' );
		}

		// Single

		if  ( is_single() ) {

			// Full width
			if ( ( get_theme_mod( 'tfm_single_full_width', false ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_full_width', true ) ) ) && in_array( 'sidebar-side', get_body_class( )) === false ) {
				$size = apply_filters( 'tfm_single_thumbnail_size', 'full' );

			} else {

				$size = $single_size;

			}

		}

		// Page

		if  ( is_page() ) {
			// Full width
			if ( ( get_theme_mod( 'tfm_page_full_width', false ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_full_width', true ) ) ) && in_array( 'sidebar-side', get_body_class( )) === false ) {
				$size = apply_filters( 'tfm_single_thumbnail_size', 'full' );
			} else {
				$size = $single_size;
			}
		}

		// ========================================================
		// w/sidebar
		// ========================================================

		if ( in_array( 'has-sidebar', get_body_class( ))) {

			if ( $post_cols === 1 ) {
				$size = apply_filters( 'tfm_archive_large_thumbnail_size', 'large' );
			}

			if ( $post_layout === 'list' ) {

				$size = apply_filters( 'tfm_archive_medium_thumbnail_size', 'medium_large' );

			}

		}

	} // end if no size declared

		// Set above the fold to eager
		$loading = $count <=2 || ($post_cols === 3 && $count <=3 ) || ($post_cols === 4 && $count <=4 ) ? 'eager' : 'lazy';

		// or permanent lazy because we have dedicated mobile thumbnails
		$loading = 'lazy';

		$mobile_loading = $count === 1 ? 'eager' : 'lazy'; // webvitals dont lazy load above the fold

		$return_thumb = the_post_thumbnail( $size, array('alt' => esc_attr( get_the_title() ), 'loading' => $loading) );
		$return_thumb .= get_theme_mod( 'tfm_mobile_thumbnail', true) && ! is_single() && ! is_page() ? the_post_thumbnail( $mobile_size, array('alt' => esc_attr( get_the_title() ), 'loading' => $mobile_loading) ) : '';

		return $return_thumb;

	}

}

// ========================================================
// Get post count for archive displays
// ========================================================

if ( ! function_exists( 'tfm_get_post_count' ) ) {

	function tfm_get_post_count() {

		$tfm_vars = tfm_template_vars( '', false );

		$html = '';

		if ( tfm_is_woo() || tfm_is_woo( 'archive' )) { // Shop and product category
			$html =  esc_attr( $tfm_vars['post_count'] ) . ' ' . esc_html__( 'Products', 'jinko' );
		} elseif ( is_category() ) {
			$html =  esc_attr( $tfm_vars['post_count'] ) . ' ' . esc_html__( 'Articles', 'jinko' );
		} elseif ( is_author() ) {
			$html =  count_user_posts ($tfm_vars['object']->ID ) . ' ' . esc_html__( 'Articles', 'jinko' );
		} elseif ( is_tag() ) {
			$html =  esc_attr( $tfm_vars['object']->count ) . ' ' . esc_html__( 'Articles', 'jinko' );
		} elseif ( is_search() ) {
			$html = esc_attr( $tfm_vars['post_count'] ) . ' ' . esc_html__( 'Articles', 'jinko' );
		} else {
			if ( is_archive() ) {
				$html =  esc_html__( 'Archive', 'jinko' );
			}
		}

		return $html;
	}

}

// ========================================================
// Remove archive title label
// ========================================================

if ( ! function_exists( 'tfm_remove_archive_title_label' ) ) {

	function tfm_remove_archive_title_label( $title ) {

		if ( get_theme_mod( 'tfm_remove_archive_title_label', true ) ) {

		    if ( is_category() ) {
		        $title = '<span>' . single_cat_title( '', false ) . '</span>';
		    } elseif ( is_tag() ) {
		        $title = '<span>' . single_tag_title( '', false ) . '</span>';
		    } elseif ( is_author() ) {
		        $title = '<span class="vcard">' . get_the_author() . '</span>';
		    } elseif ( is_post_type_archive() ) {
		        $title = '<span>' . post_type_archive_title( '', false ) . '</span>';
		    } elseif ( is_tax() ) {
		        $title = '<span>' . single_term_title( '', false ) . '</span>';
		    }

		}
	  
	    return $title;
	}

}
 
add_filter( 'get_the_archive_title', 'tfm_remove_archive_title_label' );


// ========================================================
// Blog header
// ========================================================

if ( ! function_exists( 'tfm_blog_header' ) ) { 

	function tfm_blog_header( ) {

		$tfm_vars = tfm_template_vars( '', false );

		$html = '';

		// Home

		if ( is_home( ) && ! is_paged() && have_posts( ) && get_theme_mod( 'tfm_homepage_loop_title', '' ) ) {

			$html .= '<div class="section-header home-title" data-max-width="' . esc_attr( $tfm_vars['blog_list_max_width'] ) . '">';
			$html .= '<h2 class="page-title">' . esc_html( get_theme_mod( 'tfm_homepage_loop_title', '' ) ) . '</h2>';

			if ( get_theme_mod( 'tfm_homepage_loop_subtitle', '' ) )  {
				$html .= '<p class="sub-title">' . esc_html( get_theme_mod( 'tfm_homepage_loop_subtitle', '') ) . '</p>';
			}
			$html .= '</div>';

		}


		echo wp_kses_post( $html );

	}

}
add_action('tfm_after_wrap_inner', 'tfm_blog_header', 30 );


// ========================================================
// Archive header
// ========================================================

if ( ! function_exists( 'tfm_archive_header' ) ) { 

	function tfm_archive_header( ) {

		$html = '';

		$has_avatar = is_author() && get_theme_mod( 'tfm_archive_author_avatar', true ) && get_avatar( get_the_author_meta( 'ID' )) ? ' has-avatar' : '';
		$has_background = get_theme_mod( 'tfm_archive_header_background', '') ? ' has-background' : '';
		$has_nickname = is_author() && get_theme_mod( 'tfm_archive_header_nickname', true ) ? ' has-nickname' : '';
		$has_count = get_theme_mod( 'tfm_archive_post_count', true ) ? ' has-count' : '';

		// Archive

		if ( ( get_theme_mod( 'tfm_archive_title', true ) && ( is_archive() || is_search() ) ) || tfm_is_woo( 'page' ) ) {

			$html .= '<header class="archive-header' . $has_avatar . $has_background . $has_nickname . $has_count . '">';

			$html .= '<div class="archive-header-inner">';

			// Author avatar
			if ( is_author() && get_theme_mod( 'tfm_archive_author_avatar', true ) && get_avatar( get_the_author_meta( 'ID' )) ) {
				// open a new flex wrapper
				$html .= '<div class="author-archive-wrapper">';
				$html .= '<div class="author-avatar"><span>' . get_avatar( get_the_author_meta( 'ID' ), 120 ) . '</span>';
				$html .= '</div>';
			} 

			$html .= '<div class="archive-description-wrap">';

			$html .= '<div class="archive-title-section">';

			if ( get_theme_mod( 'tfm_archive_post_count', true ) ) {

				$html .= '<span class="archive-subtitle post-count entry-meta">';

				$html .= tfm_get_post_count();

				$html .= '</span>';

			}

			if ( get_theme_mod( 'tfm_archive_title', true ) ) {


				if ( current_theme_supports( 'woocommerce') && class_exists('WooCommerce') ) {

					// Woo headers

					if ( is_shop() ) { // Shop page

						$html .= '<h1 class="archive-title"><span>' . get_the_title( get_option( 'woocommerce_shop_page_id' ) ). '</span></h1>';

					} elseif ( is_cart() || is_checkout() || is_account_page() ) { // Cart & Checkout Pages

						$html .= '<h1 class="archive-title"><span>' . get_the_title( ). '</span></h1>';

					} elseif ( is_product_category() || is_product_tag() ) { // Product category

						$html .= '<h1 class="archive-title">' . get_the_archive_title( ) . '</h1>';

					// End Woo Headers

					} elseif ( is_search() ) {

						$html .= '<h1 class="archive-title"><span>' . get_search_query() . '</span></h1>';

					} else {

						$html .= '<h1 class="archive-title">' . get_the_archive_title( ) . '</h1>';

					}

			} else {

				//  No Woo Output Theme headers

				if ( is_search() ) {

					$html .= '<h1 class="archive-title"><span>' . get_search_query() . '</span></h1>';

				} else {

					$html .= '<h1 class="archive-title">' . get_the_archive_title( ) . '</h1>';

				}

			}

			if ( is_author() && get_theme_mod( 'tfm_archive_author_nickname', true ) && '' !== get_the_author_meta( 'nickname' )) {
				$html .= '<div class="entry-meta-author-nickname">' . get_the_author_meta( 'nickname' ) . '</div>';
			}

			$html .= '</div>'; // Archive title section

			}

			// Open the desscription wrapper

			if ( ( ! is_author() && get_theme_mod( 'tfm_archive_description', true ) && '' !== get_the_archive_description( ) ) || ( is_author() && get_theme_mod( 'tfm_archive_author_bio', false ) ) || ( is_category() && get_theme_mod( 'tfm_archive_subcats', true ) ) ) {

				$html .= '<div class="archive-description-section">';

			}

			if ( ! is_author() && get_theme_mod( 'tfm_archive_description', true ) || is_author() && get_theme_mod( 'tfm_archive_author_bio', false ) ) {

				if ( tfm_is_woo( ) ) { // Shop page

					$shop_id = get_post( get_option( 'woocommerce_shop_page_id' ) );

					$html .= apply_filters('the_content', $shop_id->post_content ); 

				} else {

					if ( is_author()) {
						$html .= '<p class="archive-description">' . get_the_archive_description( ) . '</p>';
					} else {
						$html .= get_the_archive_description( );
					}

				}

			}

			// Display child categories if we have any

			$category = is_category() ? get_queried_object() : false;
			$child_categories = $category ? get_categories(
                array( 'parent' => $category->term_id,
                        'hide_empty' => false )
                        ) : false; 

			if ( is_category() && get_theme_mod( 'tfm_archive_subcats', false ) && $child_categories ) {

					$html .= '<div class="sub-categories">';

					$html .= '<ul class="child-categories">';

	                foreach ( $child_categories as $child ) {

	                	if ( 0 !== $child->count ) {

	                	$html .= '<li class="child-cat"><a class="cat-link-' . $child->term_id . '" href="' . get_category_link( $child->term_id ) . '">' . $child->cat_name . '<span class="tag-link-count child-post-count">' . $child->count . '</span></a></li>';
	                	} else {
	                		$html .= '<li></li>';
	                	}

	                }

	                $html .= '</ul>';

	                $html .= '</div>';

			}

			// Close description section
			if ( ( ! is_author() && get_theme_mod( 'tfm_archive_description', true ) && '' !== get_the_archive_description( ) ) || ( is_author() && get_theme_mod( 'tfm_archive_author_bio', false ) ) || ( is_category() && get_theme_mod( 'tfm_archive_subcats', true ) ) ) {

				$html .= '</div>';

			}

			if ( function_exists('tfm_author_social_meta') && get_theme_mod( 'tfm_archive_author_social', false)) {

				$html .= tfm_author_social_meta( true );

			}

			$html .= '</div>'; // Description wrap

			if ( is_author() && get_theme_mod( 'tfm_archive_author_avatar', true ) ) {
				$html .= '</div>'; // Close author wrapper if open
			} 

			$html .= '</div>'; // Header inner

			$html .= '</header>';


		}

		echo wp_kses_post( $html );



	}

}
add_action('tfm_before_loop', 'tfm_archive_header', 30 );


// ========================================================
// In Loop Sidebars
// ========================================================

if ( ! function_exists('tfm_in_loop_sidebar') ) {

	function tfm_in_loop_sidebar( $page, $count ) {

		if ( $page === 'front-page') {
			$page = 'home';
		}

		if ( ($page === 'home' || $page === 'front-page') && is_paged() && get_theme_mod( 'tfm_homepage_loop_sidebar_first_page_only', false ) ) {
			return;
		}

		if ( $page === 'category' && is_category() && is_paged() && get_theme_mod( 'tfm_archive_loop_sidebar_first_page_only', false ) ) {
			return;
		}
		
		if ( ( ( $page === 'home' || $page === 'front-page' ) && is_active_sidebar( 'loop-sidebar-home' ) && $count === get_theme_mod( 'tfm_homepage_loop_sidebar_position', '' ) ) ||
			 ( $page === 'category' && is_category() && is_active_sidebar( 'loop-sidebar-category' ) && $count === get_theme_mod( 'tfm_archive_loop_sidebar_position', '' ) ) ) {

			echo '<div class="article loop-sidebar sidebar">';

			echo '<div class="post-inner loop-sidebar-inner">';

			dynamic_sidebar( 'loop-sidebar-' . $page . '' );

			echo '</div>';

			echo '</div>';

		}

	}

}

// ========================================================
// Prev/Next Post Navigation
// ========================================================

if ( ! function_exists('tfm_prev_next_post') ) {

	function tfm_prev_next_post() {

		include_once( get_parent_theme_file_path( '/template-parts/post/single-post-navigation.php' )  );

	}

}

add_action('tfm_after_content', 'tfm_prev_next_post', 20 );

// ========================================================
// Author Bio
// ========================================================

if ( ! function_exists('tfm_author_bio') ) {

	function tfm_author_bio() {

		include_once( get_parent_theme_file_path( '/template-parts/post/single-authorbio.php' )  );

	}

}

add_action('tfm_after_content', 'tfm_author_bio', 10 );

// ========================================================
// Single hero layout
// ========================================================

if ( ! function_exists('tfm_single_full_width_header') ) {

	function tfm_single_full_width_header() {

		$pageID = get_option('page_on_front');

		// return if we are running post blocks on the homepage
		if ( is_front_page() && is_page( $pageID ) && function_exists( 'tfm_post_blocks') && tfm_post_blocks_active() ) {
			return;
		}

		$full_width = ( is_single() && ( get_theme_mod( 'tfm_single_full_width', false ) || ( false === get_theme_mod( 'tfm_single_full_width', false ) && function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_single_full_width', true ) ) ) ) || is_page() && ( get_theme_mod( 'tfm_page_full_width', false ) || ( false === get_theme_mod( 'tfm_page_full_width', false ) && function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_page_full_width', true ) ) ) ? true : false;

		$disabled_thumbnail = ( is_single() && ( ! get_theme_mod( 'tfm_single_thumbnail', true ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_disable_featured_media', true ) ) ) ) || ( is_page() && ( ! get_theme_mod( 'tfm_page_thumbnail', true ) || ( function_exists('tfm_custom_meta_box') && get_post_meta( get_the_ID(), 'tfm_disable_featured_media_page', true ) ) ) ) ? true : false;

		$has_post_media = tfm_featured_video( true ) || tfm_featured_audio( true ) || ( has_post_thumbnail( ) && ! $disabled_thumbnail ) ? true : false;

		if ( ( is_single() || is_page() ) && in_array( 'has-sidebar', get_body_class() ) && in_array('sidebar-after', get_body_class()) && $has_post_media ) {

			while ( have_posts() ) : the_post();

				include_once( get_parent_theme_file_path( '/template-parts/post/content-hero.php' )  );

			endwhile;
		}

	}

}

add_action('tfm_after_wrap', 'tfm_single_full_width_header', 10 );

// ========================================================
// Video post format
// ========================================================

/*
 * Get the first video embed in the post
 * Display it in place of the featured image if selected
 */

if ( ! function_exists( 'tfm_featured_video' ) ) {

	function tfm_featured_video( $has_video_query = false, $return = true ) {

		if ( ! has_post_format( 'video') ) {
			return;
		}
		// Return if media is disabled for home and archive
		if ( ( is_home() && ! get_theme_mod( 'tfm_homepage_post_media', true ) ) || ( ( is_archive() || is_search() ) && ! get_theme_mod( 'tfm_archive_post_media', true ) ) ) {
			return;
		}

		$html = '';

		if ( has_post_format( 'video') ) {

			$content = apply_filters( 'the_content', get_the_content() );
			$video = false;

			// Only get video from the content if a playlist isn't present.
			if ( false === strpos( $content, 'wp-playlist-script' ) ) {
				$video = get_media_embedded_in_content( $content, array( 'video', 'object', 'embed', 'iframe' ) );

				if ( ! $video ) {
					return false;
				}

				// We have a video and a query request
				if ( $video && $has_video_query ) {
					return true;
				}
			}

			foreach ( $video as $video_html ) {
				
				// Figure out the block type by looking at the content string

				$type = 'tfm-video'; // Default is video block

				if ( strpos($video_html, 'oembed') !== false ) {
					$type = 'tfm-video-oembed'; // block oembed (Youtube block, Vimeo block etc.)
				}
				if ( strpos($video_html, 'shortcode') !== false ) {
					$type = 'tfm-video-shortcode'; // shortcode
				}

				if ( is_single()) {
					$html .= '<div class="thumbnail-wrapper featured-media-wrapper" data-fullwidth="' .  esc_attr( tfm_full_width_featured_media( true ) ) . '">';
				}
				$html .= '<figure class="wp-block-embed is-type-video ' . $type . ' wp-embed-aspect-16-9 wp-has-aspect-ratio tfm-featured-media" data-fullwidth="' .  esc_attr( tfm_full_width_featured_media( true ) ) . '"><div class="wp-block-embed__wrapper">';
				$html .= $video_html;
				$html .= '</div></figure>';
				if ( is_single()) {
					$html .= '</div>';
				}

				return $html;

				break; // In case we have more than one embed lets break after the first iteration
			}

		} else {

			// Nothing here return false
			return false;
		}

	}

}

// ========================================================
// Audio post format
// ========================================================

/*
 * Get the first audio embed in the post
 * Display it in place of the featured image in single()
 */

if ( ! function_exists( 'tfm_featured_audio' ) ) {

	function tfm_featured_audio( $has_audio_query = false ) {

		if ( ! has_post_format( 'audio')) {
			return;
		}

		// Return if media is disabled in archive
		if ( ( is_home() && ! get_theme_mod( 'tfm_homepage_post_media', true ) ) || ( ( is_archive() || is_search() ) && ! get_theme_mod( 'tfm_archive_post_media', true ) ) ) {
			return;
		}

		if ( has_post_format( 'audio' ) && has_block('core/embed') ) {

			if ( $has_audio_query ) {
				return true;
			}

			$content = apply_filters( 'the_content', get_the_content() );
			$audio = false;

			// Only get audio from the content if a playlist isn't present.
			if ( false === strpos( $content, 'wp-playlist-script' ) ) {
				$audio = get_media_embedded_in_content( $content, array( 'audio', 'object', 'embed', 'iframe' ) );

				if ( ! $audio ) {
					return false;
				}

				// We have a audio file and a query request
				if ( $audio && $has_audio_query ) {
					return true;
				}
			}

			foreach ( $audio as $audio_html ) {

				$type = 'audio';

				if ( strpos($audio_html, 'spotify') !== false ) {
					$type = 'is-provider-spotify';
				} 
				if ( strpos($audio_html, 'mixcloud') !== false ) {
					$type = 'is-provider-mixcloud';
				}
				if ( strpos($audio_html, 'soundcloud') !== false ) {
					$type = 'is-provider-soundcloud';
				}
				if ( is_single()) {
					echo '<div class="thumbnail-wrapper featured-media-wrapper" data-fullwidth="' .  esc_attr( tfm_full_width_featured_media( true ) ) . '">';
				}
				echo '<figure class="wp-block-embed is-type-audio ' . $type . ' wp-embed-aspect-16-9 wp-has-aspect-ratio tfm-featured-media" data-fullwidth="' .  esc_attr( tfm_full_width_featured_media( true ) ) . '"><div class="wp-block-embed__wrapper">';
				echo wp_kses_post( $audio_html );
				echo '</div></figure>';
				if ( is_single()) {
				echo '</div>';
				}
				break; // In case we have more than one embed break after the first iteration
			}

		} else {

			// Nothing here return false
			return false;
		}

	}

}

// ========================================================
// Set offset for homepage loop
// ========================================================

add_action('pre_get_posts', 'tfm_query_offset', 1 );

if ( ! function_exists( 'tfm_query_offset' ) ) {

	function tfm_query_offset(&$query) {

	    //Before anything else, make sure this is the right query...
	    if ( ! $query->is_home() ) {
	        return;
	    }

	    //First, define your desired offset...
	    $offset = get_theme_mod( 'tfm_homepage_loop_offset', 0 );
	    
	    //Next, determine how many posts per page you want (we'll use WordPress's settings)
	    $ppp = get_option('posts_per_page');

	    //Next, detect and handle pagination...
	    if ( $query->is_main_query() ) {
		    if ( $query->is_paged ) {

		        //Manually determine page query offset (offset + current page (minus one) x posts per page)
		        $page_offset = $offset + ( ($query->query_vars['paged']-1) * $ppp );

		        //Apply adjust page offset
		        $query->set('offset', $page_offset );

		    }
		    else {

		        //This is the first page. Just use the offset...
		        $query->set('offset',$offset);

		    }

		}
	}

}

// ========================================================
// Offset pagination filter
// ========================================================

add_filter('found_posts', 'tfm_adjust_offset_pagination', 1, 2 );

if ( ! function_exists( 'tfm_adjust_offset_pagination' ) ) {

	function tfm_adjust_offset_pagination($found_posts, $query) {

	    //Define our offset again...
	    $offset = get_theme_mod( 'tfm_homepage_loop_offset', 0 );

	    //Ensure we're modifying the right query object...
	    if ( $query->is_home() ) {
	        //Reduce WordPress's found_posts count by the offset... 
	        return $found_posts - $offset;
	    }
	    return $found_posts;
	}

}

// ========================================================
// Modify homepage query
// ========================================================

if ( ! function_exists( 'tfm_modify_query_home' ) ) {

	function tfm_modify_query_home($query) {

	    //Before anything else, make sure this is the right query...
	    if ( ! $query->is_home() ) {
	        return;
	    }

	    $post_ids =  get_theme_mod( 'tfm_homepage_exclude_posts_ids', '' ) ? explode(',', get_theme_mod( 'tfm_homepage_exclude_posts_ids', '' ) ) : '';
	    $cat_ids =  get_theme_mod( 'tfm_homepage_exclude_cat_ids', '' ) ? explode(',', get_theme_mod( 'tfm_homepage_exclude_cat_ids', '' ) ) : '';

	    // Convert cat slugs to IDs

        if ( $cat_ids ) {

        foreach ( $cat_ids as $key => $term) {

            if ( ! is_numeric($term) ) {
                $cat = get_category_by_slug($term); 
				$term = $cat->term_id;
            }

            $terms[] = $term;

        }

        	$cat_ids = $terms;

        }

	    if ( $query->is_home() && $query->is_main_query() ) {
	        $query->set( 'post__not_in', $post_ids );
	        $query->set( 'category__not_in', $cat_ids );
	    }
	}

}
add_action('pre_get_posts', 'tfm_modify_query_home', 1);

// ========================================================
// Add iframe to allowed wp_kses_post
// ========================================================

/**
 *
 * @param array  $tags Allowed tags, attributes, and/or entities.
 * @param string $context Context to judge allowed tags by. Allowed values are 'post'.
 *
 * @return array
 */
if ( ! function_exists( 'tfm_iframe_wpkses_post_tags' ) ) {

function tfm_iframe_wpkses_post_tags( $tags, $context ) {

	if ( 'post' === $context ) {
		$tags['iframe'] = array(
			'src'             => true,
			'height'          => true,
			'width'           => true,
			'frameborder'     => true,
			'allowfullscreen' => true,
			'allow'           => true,
		);
	}

	return $tags;
}

}

add_filter( 'wp_kses_allowed_html', 'tfm_iframe_wpkses_post_tags', 10, 2 );

// ========================================================
// Modify WP Widgets HTML output
// ========================================================

// Categories widget add span around post count
function tfm_cat_widget_count_span( $links ) {

	$links = str_replace( '</a> (', '<span class="tfm-count">', $links );
	$links = str_replace( ')', ' <i>' . esc_html__( 'Posts', 'jinko' ) . '</i></span></a>', $links );
	return $links;

}
add_filter( 'wp_list_categories', 'tfm_cat_widget_count_span' );

// Archives widget add span around post count
function tfm_archive_widget_count_span( $links ) {

	$links = str_replace( '</a>&nbsp;(', '<span class="tfm-count"><i class="hidden">(</i>', $links );
	$links = str_replace( ')', ' <i>' . esc_html__( 'Posts', 'jinko' ) . '</i><i class="hidden">)</i></span></a>', $links );
	return $links;

}
add_filter( 'get_archives_link', 'tfm_archive_widget_count_span' );


// ========================================================
// Display primary menu description field
// ========================================================

if ( ! function_exists('tfm_primary_menu_desc')) {

	function tfm_primary_menu_desc( $item_output, $item, $depth, $args ) {

		$cat_menu_item = $item->object === 'category' ? 'cat-link-' . $item->object_id : false;
		$category = $cat_menu_item ? get_category($item->object_id) : false;
		$count = $cat_menu_item && get_theme_mod( 'tfm_primary_menu_show_cat_count', false ) ? $category->category_count : false;

		if ( $cat_menu_item) {
			$item_output = str_replace( '<a href', '<a class="' . $cat_menu_item . '" href', $item_output );
			if ( $count ) {
				$item_output = str_replace( '</a>', '<span class="menu-description count"><span class="count ' . $cat_menu_item . '">' . $count . '</span> ' . esc_html__( 'posts', 'jinko' ) . '</span></a>', $item_output );
			}
		}
		if ( ( 'primary' == $args->theme_location || 'split-menu-left' == $args->theme_location || 'split-menu-right' == $args->theme_location || 'slide-menu-primary' == $args->theme_location ) && $item->description )
			$item_output = str_replace( '</a>', '<span class="menu-description">' . $item->description . '</span></a>', $item_output );
			
		return $item_output;
	}
}

add_filter( 'walker_nav_menu_start_el', 'tfm_primary_menu_desc', 10, 4 );

// ========================================================
// Count parent menu items 
// ========================================================

/**
 * Gets the count of the parent items in a nav menu based upon the id specified.
 * 
 * @param 	string $nav_id The id of the nav item to get the parent count for.
 * @return 	bool|int False on fail, or the count of how many parent items there are.
 * @uses 	wp_get_nav_menu_items
 */

function tfm_count_nav_parent_items( $nav_id = null ) {

	if( $nav_id === null || $nav_id === '' ) {
		return false;
	}

	$items = wp_get_nav_menu_items( $nav_id );

	if( $items && count( $items ) <= 0 ) {
		return false;
	}

	// go through each item
	$parents = array();
	if ( $items ) {
		
		foreach( $items as $item ) {

			// if the item's parent is 0, it's a top level, add it to an array
			if( $item->menu_item_parent == 0 )
				$parents[] = $item;
		}

	}

	return count( $parents );
}

// ========================================================
// Add menu item count class to a nav container 
// ========================================================

/**
 * Adds a class to a nav container based upon how many parent items it contains.
 * 
 * @param 	array $args The array of args supplied by the filter for the nav menu.
 * @return 	array Returns the same array passed in, just modified
 * @uses 	get_nav_menu_locations, count_nav_parent_items
 */

function add_nav_parent_count( $args = '' ) {
	
	// is the id in the args?
	if( isset( $args[ 'menu' ]->term_id ) && $args[ 'menu' ]->term_id !== 0 ) {

		// if so, set it
		$menu_id = $args[ 'menu' ]->term_id;

	// do we have a theme location?
	} elseif( isset( $args[ 'theme_location' ] ) && $args[ 'theme_location' ] !== '' ) {

		// get all the locations
		$theme_nav_locations = get_nav_menu_locations();

		// if we don't have any theme locations, exit
		if( count( $theme_nav_locations ) <= 0) {

			// add a theme locations not found class and exit
			$args[ 'menu_class' ] = $args[ 'menu_class' ] .' parent-items-tlnf';
			return $args;
		}

		// set the menu id
		$menu_id = $theme_nav_locations[ $args[ 'theme_location' ] ];

	// we have nothing
	} else {

		// add an id not found class and exit
		$args[ 'menu_class' ] = $args[ 'menu_class' ] .' parent-items-idnf';
		return $args;
	}

	// count the parents
	$parent_count = tfm_count_nav_parent_items( $menu_id );

	// if we got a count
	if( $parent_count ) {

		// add the class to the nav container
		$args[ 'menu_class' ] = $args[ 'menu_class' ] .' parent-items-'. $parent_count;

	// we didn't
	} else {

		// add a different class to the nav container
		$args[ 'menu_class' ] = $args[ 'menu_class' ] .' parent-items-pcnf';
	}

	// put the array back into play
	return $args;
}
add_filter( 'wp_nav_menu_args', 'add_nav_parent_count' );

// ========================================================
// Check if this is a woocommerce page
// ========================================================

if ( ! function_exists( 'tfm_is_woo' ) ) {

	function tfm_is_woo( $woo_page ='' ) {

		$is_woo = false;

		if ( current_theme_supports( 'woocommerce') && class_exists('WooCommerce')) {

			// Shop
			if ( '' === $woo_page && is_shop() ) {
				$is_woo = true;
			}

			// Product page
			if ( $woo_page === 'product' && is_product() ) {
				$is_woo = true;
			}
			// Category/archive
			if ( $woo_page === 'archive' && ( is_product_category() || is_product_tag() ) ) {
				$is_woo = true;
			}
			// Cart/Checkout/Account
			if ( $woo_page === 'page' && ( is_cart() || is_checkout() || is_account_page() ) ) {
				$is_woo = true;
			}

		}

		return $is_woo;
	}

}

// ========================================================
// Convert Hex to RGBa
// ========================================================

function tfm_hex2rgba( $colour, $alpha = 1, $array = false ) {

        if ( $colour[0] == '#' ) {
                $colour = substr( $colour, 1 );
        }
        if ( strlen( $colour ) == 6 ) {
            list( $r, $g, $b ) = array( $colour[0] . $colour[1], $colour[2] . $colour[3], $colour[4] . $colour[5] );
        } elseif ( strlen( $colour ) == 3 ) {
            list( $r, $g, $b ) = array( $colour[0] . $colour[0], $colour[1] . $colour[1], $colour[2] . $colour[2] );
        } else {
            return false;
        }

        $r = hexdec( $r );
        $g = hexdec( $g );
        $b = hexdec( $b );

        if ( $array ) {
	        return array( 'red' => $r, 'green' => $g, 'blue' => $b );
	    } else {
	    	if ( $alpha === 1 ) {
	    		return 'rgb(' . $r .',' . $g . ',' . $b . ')';
	    	} else {
		    	return 'rgba(' . $r .',' . $g . ',' . $b . ',' . $alpha . ')';
		    }
	    }

}

// ========================================================
// Calculate light dark based on RGB value
// ========================================================

function tfm_is_light_dark($hexcolor='') {

	if ('' === $hexcolor ) {
		return false;
	}

	if ( $hexcolor[0] == '#' ) {
        $hexcolor = substr( $hexcolor, 1 );
    }

	$r = hexdec(substr($hexcolor,0,2));
	$g = hexdec(substr($hexcolor,2,2));
	$b = hexdec(substr($hexcolor,4,2));

	$threshold = 400; // higher is light

	if($r + $g + $b > $threshold ){
    	return 'tfm-is-light';
	} else{
	    return 'tfm-is-dark';
	}

}

// ========================================================
// Add alt tag to gravtar (seo/web vitals)
// ========================================================

function tfm_gravatar_alt_text($text) {
    $alt = get_the_author_meta( 'display_name' );
    $text = str_replace('alt=\'\'', 'alt=\''.$alt.'\' title=\''.$alt.'\'',$text);
    return $text;
}
add_filter('get_avatar','tfm_gravatar_alt_text');

// ========================================================
// Remove dashicons in frontend if not logged in
// ========================================================

add_action( 'wp_enqueue_scripts', 'tfm_dequeue_dashicons' );
function tfm_dequeue_dashicons() {

    if ( ! is_user_logged_in() ) {
        wp_deregister_style( 'dashicons' );
    }

}

// ========================================================
// Customizer and core functions
// ========================================================
require get_parent_theme_file_path( '/inc/theme-settings.php' );
require get_parent_theme_file_path( '/inc/template-vars.php' );
require get_parent_theme_file_path( '/inc/hooks.php' );
require get_parent_theme_file_path( '/inc/plugin-filters.php' );
require get_parent_theme_file_path( '/inc/customizer/customizer.php' );
// color settings
require get_parent_theme_file_path( '/inc/customizer/customizer_colors.php' );
require get_parent_theme_file_path( '/inc/css/custom_css.php' );
require get_parent_theme_file_path( '/inc/gutenberg_color_palette.php' );
// misc
require get_parent_theme_file_path( '/inc/sanitization.php' );
require get_parent_theme_file_path( '/inc/tgmpa.php' );
require get_parent_theme_file_path( '/inc/ocdi.php' );
// Woocommerce
if ( current_theme_supports( 'woocommerce') && class_exists('WooCommerce')) {
	require get_parent_theme_file_path( '/inc/woocommerce-functions.php' );
}

?>