/**
 * تنسيقات الواجهة الأمامية - إضافة مدير الأنمي
 * متوافق مع قالب Jinko
 */

/* البطاقة الرئيسية لتفاصيل الأنمي */
.anime-details-card {
    background: var(--white, #ffffff);
    border-radius: var(--default-border-radius, 5px);
    box-shadow: var(--post-box-shadow, 0 0 20px rgba(0,0,0,0.07));
    margin-bottom: var(--post-margin, 2.5rem);
    overflow: hidden;
    border: 1px solid var(--default-border-color, rgba(0,0,0,0.08));
    transition: all 0.3s ease;
}

.anime-details-card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

/* رأس البطاقة */
.anime-details-header {
    background: linear-gradient(135deg, var(--primary-theme-color, #6c5b7b), var(--secondary-theme-color, #f67280));
    padding: 1.5rem var(--card-padding, 1.875rem);
    color: var(--white, #ffffff);
}

.anime-details-title {
    margin: 0;
    font-size: var(--h4-font-size, 1.5rem);
    font-weight: var(--heading-font-weight, 700);
    font-family: var(--title-font, "Jost", Arial, Helvetica, sans-serif);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.anime-details-icon {
    font-size: 1.75rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

/* محتوى البطاقة */
.anime-details-content {
    padding: var(--card-padding, 1.875rem);
}

/* الشبكة الرئيسية */
.anime-details-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--post-margin, 2.5rem);
    margin-bottom: var(--post-margin, 2.5rem);
}

/* قسم صورة الغلاف */
.anime-cover-section {
    min-width: 200px;
    max-width: 300px;
}

.anime-cover-wrapper {
    position: relative;
    border-radius: var(--post-thumbnail-border-radius, 5px);
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
}

.anime-cover-wrapper:hover {
    transform: scale(1.02);
}

.anime-cover-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: var(--post-thumbnail-border-radius, 5px);
}

/* قسم المعلومات */
.anime-info-section {
    flex: 1;
}

.anime-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
}

/* عنصر المعلومات */
.anime-info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--very-light-grey, #f2f2f3);
    border-radius: var(--default-border-radius, 5px);
    border-right: 4px solid var(--primary-theme-color, #6c5b7b);
    transition: all 0.3s ease;
}

.anime-info-item:hover {
    background: var(--off-white, #f7f8fa);
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.anime-info-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--body-font-color, #131315);
    font-family: var(--title-font, "Jost", Arial, Helvetica, sans-serif);
    font-size: 0.95rem;
}

.anime-info-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.anime-info-value {
    font-weight: 500;
    color: var(--dark-grey, #44464b);
    font-size: 0.95rem;
    text-align: left;
}

/* ألوان مخصصة للأنواع */
.anime-type-تلفازي {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.anime-type-بلوراي {
    color: #7c3aed;
    background: rgba(124, 58, 237, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.anime-type-ويب {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

/* ألوان مخصصة للحالات */
.anime-status-مستمر {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.anime-status-مكتمل {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.anime-status-متوقف {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.anime-status-ملغي {
    color: #7f1d1d;
    background: rgba(127, 29, 29, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.anime-status-قادم-لاحقًا {
    color: #d97706;
    background: rgba(217, 119, 6, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

/* قسم الروابط الخارجية */
.anime-links-section {
    border-top: 1px solid var(--default-border-color, rgba(0,0,0,0.08));
    padding-top: var(--post-margin, 2.5rem);
    margin-top: var(--post-margin, 2.5rem);
}

.anime-links-title {
    margin: 0 0 1.5rem 0;
    font-size: var(--h5-font-size, 1.25rem);
    font-weight: var(--heading-font-weight, 700);
    color: var(--body-font-color, #131315);
    font-family: var(--title-font, "Jost", Arial, Helvetica, sans-serif);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.anime-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

/* الروابط الخارجية */
.anime-external-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    background: var(--white, #ffffff);
    border: 2px solid var(--default-border-color, rgba(0,0,0,0.08));
    border-radius: var(--default-border-radius, 5px);
    text-decoration: none;
    color: var(--body-font-color, #131315);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.anime-external-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-theme-color, #6c5b7b), var(--secondary-theme-color, #f67280));
    transition: width 0.3s ease;
    z-index: 0;
}

.anime-external-link:hover::before {
    width: 100%;
}

.anime-external-link:hover {
    color: var(--white, #ffffff);
    border-color: var(--primary-theme-color, #6c5b7b);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.anime-link-icon,
.anime-link-text,
.anime-link-arrow {
    position: relative;
    z-index: 1;
}

.anime-link-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.anime-link-text {
    flex: 1;
    font-weight: 500;
    font-size: 0.95rem;
}

.anime-link-arrow {
    font-size: 1.1rem;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.anime-external-link:hover .anime-link-arrow {
    transform: translate(3px, -3px);
}

/* التجاوب مع الشاشات المختلفة */
@media (max-width: 768px) {
    .anime-details-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }
    
    .anime-cover-section {
        max-width: 250px;
        margin: 0 auto;
    }
    
    .anime-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .anime-info-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
        padding: 1.25rem;
    }
    
    .anime-info-label,
    .anime-info-value {
        text-align: center;
    }
    
    .anime-links-grid {
        grid-template-columns: 1fr;
    }
    
    .anime-details-content {
        padding: 1.25rem;
    }
    
    .anime-details-header {
        padding: 1.25rem;
    }
}

@media (max-width: 480px) {
    .anime-details-title {
        font-size: 1.25rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .anime-external-link {
        padding: 0.875rem 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .anime-link-text {
        font-size: 0.875rem;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .anime-details-card {
        background: var(--very-dark-grey, #131315);
        border-color: var(--dark-grey, #44464b);
    }
    
    .anime-info-item {
        background: var(--dark-grey, #44464b);
        color: var(--white, #ffffff);
    }
    
    .anime-info-item:hover {
        background: var(--medium-grey, #94979e);
    }
    
    .anime-external-link {
        background: var(--very-dark-grey, #131315);
        border-color: var(--dark-grey, #44464b);
        color: var(--white, #ffffff);
    }
}

/* تأثيرات إضافية للتفاعل */
.anime-details-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين إمكانية الوصول */
.anime-external-link:focus {
    outline: 2px solid var(--primary-theme-color, #6c5b7b);
    outline-offset: 2px;
}

.anime-info-item:focus-within {
    outline: 2px solid var(--primary-theme-color, #6c5b7b);
    outline-offset: 2px;
}

/* طباعة */
@media print {
    .anime-details-card {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .anime-external-link {
        border: 1px solid #000;
    }
    
    .anime-external-link::after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
}
