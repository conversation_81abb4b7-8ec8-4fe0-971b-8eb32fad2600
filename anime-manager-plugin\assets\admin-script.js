/**
 * JavaScript للوحة الإدارة - إضافة مدير الأنمي
 */

(function($) {
    'use strict';

    // متغير لتتبع عدد الروابط
    let linkCounter = 0;

    $(document).ready(function() {
        initAnimeManager();
    });

    /**
     * تهيئة مدير الأنمي
     */
    function initAnimeManager() {
        // تهيئة عداد الروابط
        linkCounter = $('#anime-links-container .anime-link-item').length;
        
        // ربط الأحداث
        bindEvents();
        
        // تحسين تجربة المستخدم
        enhanceUserExperience();
    }

    /**
     * ربط الأحداث
     */
    function bindEvents() {
        // إضافة رابط جديد
        $(document).on('click', '#anime-add-link', function(e) {
            e.preventDefault();
            addNewLink();
        });

        // حذف رابط
        $(document).on('click', '.anime-remove-link', function(e) {
            e.preventDefault();
            removeLink($(this));
        });

        // التحقق من صحة البيانات عند الكتابة
        $(document).on('input', '.anime-link-url', function() {
            validateUrl($(this));
        });

        // التحقق من الحقول الرقمية
        $(document).on('input', '.anime-number-field', function() {
            validateNumber($(this));
        });

        // حفظ تلقائي للمسودة
        $(document).on('change', '.anime-select-field, .anime-number-field', function() {
            triggerAutosave();
        });
    }

    /**
     * إضافة رابط جديد
     */
    function addNewLink() {
        const linkHtml = `
            <div class="anime-link-item" style="display: none;">
                <div class="anime-link-inputs">
                    <input type="text" 
                           name="_anime_links[${linkCounter}][title]" 
                           placeholder="عنوان الرابط (مثال: الموقع الرسمي)"
                           class="anime-link-title">
                    <input type="url" 
                           name="_anime_links[${linkCounter}][url]" 
                           placeholder="https://example.com"
                           class="anime-link-url">
                </div>
                <button type="button" class="anime-remove-link" title="حذف الرابط">
                    <span class="dashicons dashicons-trash"></span>
                </button>
            </div>
        `;

        const $newLink = $(linkHtml);
        $('#anime-links-container').append($newLink);
        
        // إظهار الرابط الجديد بتأثير انزلاق
        $newLink.slideDown(300, function() {
            // التركيز على حقل العنوان
            $newLink.find('.anime-link-title').focus();
        });

        linkCounter++;
        
        // تحديث أرقام الفهارس
        updateLinkIndexes();
    }

    /**
     * حذف رابط
     */
    function removeLink($button) {
        const $linkItem = $button.closest('.anime-link-item');
        
        // تأكيد الحذف إذا كان الرابط يحتوي على بيانات
        const title = $linkItem.find('.anime-link-title').val();
        const url = $linkItem.find('.anime-link-url').val();
        
        if ((title || url) && !confirm('هل أنت متأكد من حذف هذا الرابط؟')) {
            return;
        }

        // حذف الرابط بتأثير انزلاق
        $linkItem.slideUp(300, function() {
            $(this).remove();
            updateLinkIndexes();
        });
    }

    /**
     * تحديث فهارس الروابط
     */
    function updateLinkIndexes() {
        $('#anime-links-container .anime-link-item').each(function(index) {
            $(this).find('input[name*="[title]"]').attr('name', `_anime_links[${index}][title]`);
            $(this).find('input[name*="[url]"]').attr('name', `_anime_links[${index}][url]`);
        });
    }

    /**
     * التحقق من صحة الرابط
     */
    function validateUrl($input) {
        const url = $input.val();
        const $fieldGroup = $input.closest('.anime-link-item');
        
        // إزالة رسائل الخطأ السابقة
        $fieldGroup.removeClass('has-error');
        $input.siblings('.anime-field-error').remove();

        if (url && !isValidUrl(url)) {
            $fieldGroup.addClass('has-error');
            $input.after('<span class="anime-field-error">الرجاء إدخال رابط صحيح</span>');
        }
    }

    /**
     * التحقق من صحة الأرقام
     */
    function validateNumber($input) {
        const value = parseInt($input.val());
        const min = parseInt($input.attr('min'));
        const max = parseInt($input.attr('max'));
        const $fieldGroup = $input.closest('.anime-field-group');
        
        // إزالة رسائل الخطأ السابقة
        $fieldGroup.removeClass('has-error');
        $input.siblings('.anime-field-error').remove();

        if ($input.val() && (isNaN(value) || (min && value < min) || (max && value > max))) {
            $fieldGroup.addClass('has-error');
            let errorMsg = 'الرجاء إدخال رقم صحيح';
            if (min && max) {
                errorMsg += ` بين ${min} و ${max}`;
            } else if (min) {
                errorMsg += ` أكبر من ${min}`;
            } else if (max) {
                errorMsg += ` أصغر من ${max}`;
            }
            $input.after(`<span class="anime-field-error">${errorMsg}</span>`);
        }
    }

    /**
     * التحقق من صحة الرابط
     */
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    /**
     * تحسين تجربة المستخدم
     */
    function enhanceUserExperience() {
        // إضافة تلميحات للحقول
        addTooltips();
        
        // تحسين التنقل بالكيبورد
        enhanceKeyboardNavigation();
        
        // إضافة اختصارات لوحة المفاتيح
        addKeyboardShortcuts();
    }

    /**
     * إضافة تلميحات للحقول
     */
    function addTooltips() {
        // تلميحات للحقول الرقمية
        $('.anime-number-field').each(function() {
            const $this = $(this);
            const min = $this.attr('min');
            const max = $this.attr('max');
            
            if (min || max) {
                let tooltip = 'القيم المسموحة: ';
                if (min && max) {
                    tooltip += `من ${min} إلى ${max}`;
                } else if (min) {
                    tooltip += `من ${min} فما فوق`;
                } else if (max) {
                    tooltip += `حتى ${max}`;
                }
                $this.attr('title', tooltip);
            }
        });
    }

    /**
     * تحسين التنقل بالكيبورد
     */
    function enhanceKeyboardNavigation() {
        // التنقل بين الحقول باستخدام Enter
        $('.anime-link-title, .anime-link-url').on('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const $next = $(this).closest('.anime-link-inputs').find('input').not(this).first();
                if ($next.length) {
                    $next.focus();
                } else {
                    // إذا كان هذا آخر حقل، أضف رابط جديد
                    addNewLink();
                }
            }
        });
    }

    /**
     * إضافة اختصارات لوحة المفاتيح
     */
    function addKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + Shift + L لإضافة رابط جديد
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'L') {
                e.preventDefault();
                addNewLink();
            }
        });
    }

    /**
     * تشغيل الحفظ التلقائي
     */
    function triggerAutosave() {
        if (typeof wp !== 'undefined' && wp.autosave) {
            wp.autosave.server.triggerSave();
        }
    }

    /**
     * إضافة تأثيرات بصرية للتفاعل
     */
    function addVisualEffects() {
        // تأثير عند التركيز على الحقول
        $('.anime-select-field, .anime-number-field, .anime-link-title, .anime-link-url')
            .on('focus', function() {
                $(this).closest('.anime-field-group, .anime-link-item').addClass('focused');
            })
            .on('blur', function() {
                $(this).closest('.anime-field-group, .anime-link-item').removeClass('focused');
            });
    }

    // تشغيل التأثيرات البصرية
    addVisualEffects();

    /**
     * معالجة الأخطاء العامة
     */
    window.addEventListener('error', function(e) {
        console.error('خطأ في مدير الأنمي:', e.error);
    });

})(jQuery);
