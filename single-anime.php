<?php
/**
 * قالب مخصص لعرض صفحات الأنمي المفردة - تصميم احترافي عصري
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 */
?>
<style>
/* استيراد خطوط Google Fonts العصرية */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
  /* ألوان عصرية احترافية */
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* تدرجات لونية */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);

  /* ألوان النص والخلفية */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  /* تأثيرات بصرية */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* خطوط */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Poppins', sans-serif;
}

/* إعدادات عامة */
* {
  box-sizing: border-box;
}

/* التصميم الأساسي للصفحة */
.anime-page {
  font-family: var(--font-primary);
  background: var(--bg-secondary);
  min-height: 100vh;
  color: var(--text-primary);
  line-height: 1.6;
}

.anime-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* رأس الصفحة - الصورة والمعلومات الأساسية */
.anime-hero {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
}

/* صورة الغلاف */
.anime-cover-container {
  position: relative;
}

.anime-cover {
  width: 100%;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  aspect-ratio: 3/4;
  object-fit: cover;
}

.anime-cover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* معلومات الأنمي الأساسية */
.anime-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.anime-title {
  font-family: var(--font-secondary);
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  line-height: 1.2;
}

.anime-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.rating-stars {
  color: #fbbf24;
}

.rating-value {
  font-weight: 600;
  color: var(--text-secondary);
}

.anime-summary {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-style: italic;
  line-height: 1.7;
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary);
}

/* شبكة المعلومات السريعة */
.anime-quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.stat-item:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-light);
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.stat-item:hover .stat-label {
  color: rgba(255, 255, 255, 0.8);
}

.stat-value {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-item:hover .stat-value {
  color: white;
}

/* قسم التفاصيل الكاملة */
.anime-details {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
}

.details-title {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.detail-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: var(--transition);
  border: 2px solid transparent;
}

.detail-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.detail-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.detail-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.detail-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* الروابط الخارجية */
.anime-links {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid var(--bg-tertiary);
}

.links-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.external-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition);
  border: 2px solid transparent;
}

.external-link:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary-dark);
  transform: translateY(-2px);
}

.link-icon {
  font-size: 1.2rem;
}

.link-text {
  flex: 1;
  text-align: center;
  font-weight: 600;
}

.link-arrow {
  font-size: 1rem;
  transition: var(--transition);
}

.external-link:hover .link-arrow {
  transform: translate(2px, -2px);
}

/* محتوى القصة */
.anime-story {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
}

.story-title {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.story-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-secondary);
}

.story-content p {
  margin-bottom: 1.5rem;
}

.story-content h1,
.story-content h2,
.story-content h3 {
  font-family: var(--font-secondary);
  color: var(--text-primary);
  margin: 2rem 0 1rem 0;
}

/* تحسينات الاستجابة */
@media (max-width: 1024px) {
  .anime-hero {
    grid-template-columns: 300px 1fr;
    gap: 2rem;
  }

  .anime-container {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .anime-hero {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .anime-cover-container {
    max-width: 300px;
    margin: 0 auto;
  }

  .anime-quick-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .links-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .anime-container {
    padding: 1rem;
  }

  .anime-hero,
  .anime-details,
  .anime-story {
    padding: 1.5rem;
  }

  .anime-quick-stats {
    grid-template-columns: 1fr;
  }

  .anime-cover-container {
    max-width: 250px;
  }
}

/* تقييم الأنمي */
.anime-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--light-color);
  border-radius: var(--border-radius);
}

.rating-stars {
  color: #ffd700;
  font-size: 1.2rem;
}

.rating-text {
  font-weight: 600;
  color: var(--text-color);
}

/* ملخص سريع */
.anime-quick-summary {
  color: var(--text-light);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-style: italic;
}

/* المعلومات السريعة */
.anime-quick-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.quick-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--light-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.quick-info-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.info-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}

/* قسم التفاصيل العصري */
.modern-details-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-lg);
  padding: 3rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.modern-details-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.anime-details-grid-custom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.modern-detail-item {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.modern-detail-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--secondary-gradient);
  transform: translateX(-100%);
  transition: var(--transition);
}

.modern-detail-item:hover::before {
  transform: translateX(0);
}

.modern-detail-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--box-shadow-lg);
  border-color: rgba(102, 126, 234, 0.2);
}

.modern-detail-icon {
  font-size: 1.5rem;
  margin-left: 0.75rem;
  filter: grayscale(0);
  transition: var(--transition);
}

.modern-detail-item:hover .modern-detail-icon {
  transform: scale(1.2) rotate(5deg);
}

.modern-detail-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0.5rem;
}

.modern-detail-value {
  font-weight: 500;
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* الروابط الخارجية بتصميم عصري */
.anime-links-section-custom {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid rgba(102, 126, 234, 0.1);
}

.anime-links-title-custom {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.anime-links-grid-custom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.anime-external-link-custom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.anime-external-link-custom::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--success-gradient);
  transition: var(--transition);
  z-index: -1;
}

.anime-external-link-custom:hover::before {
  left: 0;
}

.anime-external-link-custom:hover {
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
  border-color: rgba(79, 172, 254, 0.3);
}

.anime-link-icon-custom {
  font-size: 1.2rem;
  margin-left: 0.5rem;
  transition: var(--transition);
}

.anime-external-link-custom:hover .anime-link-icon-custom {
  transform: scale(1.2);
}

.anime-link-text-custom {
  flex: 1;
  text-align: center;
  font-weight: 600;
}

.anime-link-arrow-custom {
  font-size: 1.1rem;
  transition: var(--transition);
}

.anime-external-link-custom:hover .anime-link-arrow-custom {
  transform: translateX(5px) translateY(-5px);
}

/* محتوى القصة بتصميم عصري */
.anime-story-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  padding: 3rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  line-height: 1.8;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.anime-story-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--accent-color);
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

.story-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.story-title {
  font-family: var(--font-secondary);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.story-icon {
  font-size: 1.5rem;
  filter: grayscale(0);
}

.anime-story-content .entry-content {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.8;
}

.anime-story-content .entry-content p {
  margin-bottom: 1.5rem;
}

.anime-story-content .entry-content h1,
.anime-story-content .entry-content h2,
.anime-story-content .entry-content h3 {
  font-family: var(--font-secondary);
  font-weight: 700;
  color: var(--text-color);
  margin: 2rem 0 1rem 0;
}

/* تحسينات للاستجابة */
@media (max-width: 1200px) {
  .modern-anime-container {
    padding: 2rem 1.5rem;
  }

  .anime-header-layout {
    gap: 2rem;
  }

  .modern-hero-image {
    width: 350px;
    max-width: 350px;
  }

  .modern-hero-image img {
    min-height: 450px;
  }
}

@media (max-width: 1024px) {
  .anime-header-layout {
    gap: 2rem;
  }

  .modern-hero-image {
    width: 320px;
    max-width: 320px;
  }

  .modern-hero-image img {
    min-height: 420px;
  }

  .modern-details-section {
    padding: 2rem;
  }

  .anime-details-grid-custom {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .modern-anime-container {
    padding: 1.5rem 1rem;
  }

  .anime-header-layout {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .modern-hero-image {
    width: 300px;
    max-width: 300px;
    margin: 0 auto;
  }

  .modern-hero-image img {
    min-height: 400px;
  }

  .anime-main-info {
    padding: 2rem;
    min-height: auto;
  }

  .modern-main-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
  }

  .anime-quick-info {
    grid-template-columns: repeat(2, 1fr);
  }

  .anime-details-grid-custom {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modern-detail-item {
    padding: 1.5rem;
  }

  .anime-links-grid-custom {
    grid-template-columns: 1fr;
  }

  .anime-story-content {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .modern-anime-container {
    padding: 1rem 0.5rem;
  }

  .anime-header-layout {
    gap: 1.5rem;
  }

  .modern-hero-image {
    width: 250px;
    max-width: 250px;
  }

  .modern-hero-image img {
    min-height: 350px;
  }

  .anime-main-info {
    padding: 1.5rem;
  }

  .anime-quick-info {
    grid-template-columns: 1fr;
  }

  .modern-details-section,
  .anime-story-content {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .modern-detail-item {
    padding: 1rem;
  }

  .anime-external-link-custom {
    padding: 1rem;
  }
}

/* تأثيرات إضافية للتفاعل */
.modern-detail-item,
.anime-external-link-custom,
.anime-story-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للطباعة */
@media print {
  .modern-anime-layout::before {
    display: none;
  }

  .modern-hero-image,
  .modern-details-section,
  .anime-story-content {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .modern-main-title {
    color: #333 !important;
    -webkit-text-fill-color: #333 !important;
  }
}

/* تأثيرات التمرير السلس */
html {
  scroll-behavior: smooth;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #e2e8f0;
    --text-light: #a0aec0;
    --light-color: #2d3748;
    --white: #1a202c;
  }

  .modern-anime-layout {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
}
</style>
<?php

/**
 * تحميل ووردبريس الأساسي
 * نحدد المسار الجذر لووردبريس أولاً
 */
$wp_root_path = realpath(dirname(dirname(dirname(dirname(__FILE__)))));

// التحقق من وجود ملف wp-config.php
$wp_config_path = $wp_root_path . '/wp-config.php';
if (!file_exists($wp_config_path)) {
    die('Error: Unable to locate WordPress configuration file (wp-config.php)');
}

require_once($wp_config_path);

// تعريف ABSPATH إذا لم يكن معرّفاً
if (!defined('ABSPATH')) {
    define('ABSPATH', $wp_root_path . '/');
}

// تحميل ملف wp-settings.php
$wp_settings_path = ABSPATH . 'wp-settings.php';
if (!file_exists($wp_settings_path)) {
    die('Error: Unable to locate WordPress wp-settings.php file');
}

require_once($wp_settings_path);

get_header();

$customizer_settings = tfm_general_settings();
$widget_background = $customizer_settings['tfm_widget_background'] !== get_theme_mod( 'tfm_widget_background', $customizer_settings['tfm_widget_background'] ) ? ' has-background' : '';
$widget_color = $customizer_settings['tfm_widget_color']  !== get_theme_mod( 'tfm_widget_color', $customizer_settings['tfm_widget_color'] ) ? ' has-custom-color' : '';
$widget_link_color = $customizer_settings['tfm_widget_link_color'] !== get_theme_mod( 'tfm_widget_link_color', $customizer_settings['tfm_widget_link_color'] ) ? ' has-custom-link-color' : '';

?>

<main class="anime-page">
    <div class="anime-container">

        <?php while ( have_posts() ) : the_post(); ?>

            <!-- رأس الصفحة - الصورة والمعلومات الأساسية -->
            <section class="anime-hero">

                <!-- صورة الغلاف -->
                <div class="anime-cover-container">
                    <?php if (has_post_thumbnail()): ?>
                        <?php
                        the_post_thumbnail('large', array(
                            'class' => 'anime-cover',
                            'alt' => get_the_title(),
                            'loading' => 'lazy'
                        ));
                        ?>
                    <?php endif; ?>
                </div>

                <!-- معلومات الأنمي الأساسية -->
                <div class="anime-info">

                    <!-- عنوان الأنمي -->
                    <h1 class="anime-title"><?php the_title(); ?></h1>

                    <!-- تقييم الأنمي -->
                    <?php
                    $anime_rating = get_post_meta(get_the_ID(), '_anime_rating', true);
                    if (!empty($anime_rating)):
                    ?>
                        <div class="anime-rating">
                            <div class="rating-stars">
                                <?php
                                $rating = floatval($anime_rating);
                                $full_stars = floor($rating);

                                for ($i = 1; $i <= 5; $i++) {
                                    if ($i <= $full_stars) {
                                        echo '★';
                                    } else {
                                        echo '☆';
                                    }
                                }
                                ?>
                            </div>
                            <span class="rating-value"><?php echo esc_html($anime_rating); ?>/5</span>
                        </div>
                    <?php endif; ?>

                    <!-- ملخص سريع -->
                    <?php
                    $anime_summary = get_post_meta(get_the_ID(), '_anime_summary', true);
                    if (!empty($anime_summary)):
                    ?>
                        <div class="anime-summary">
                            <?php echo esc_html($anime_summary); ?>
                        </div>
                    <?php endif; ?>

                    <!-- الإحصائيات السريعة -->
                    <?php
                    $anime_type = get_post_meta(get_the_ID(), '_anime_type', true);
                    $anime_status = get_post_meta(get_the_ID(), '_anime_status', true);
                    $anime_episodes = get_post_meta(get_the_ID(), '_anime_episodes', true);
                    $anime_year = get_post_meta(get_the_ID(), '_anime_year', true);

                    $has_stats = !empty($anime_type) || !empty($anime_status) || !empty($anime_episodes) || !empty($anime_year);

                    if ($has_stats): ?>
                        <div class="anime-quick-stats">
                            <?php if (!empty($anime_type)): ?>
                                <div class="stat-item">
                                    <div class="stat-label">النوع</div>
                                    <div class="stat-value"><?php echo esc_html($anime_type); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($anime_status)): ?>
                                <div class="stat-item">
                                    <div class="stat-label">الحالة</div>
                                    <div class="stat-value"><?php echo esc_html($anime_status); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($anime_episodes)): ?>
                                <div class="stat-item">
                                    <div class="stat-label">الحلقات</div>
                                    <div class="stat-value"><?php echo esc_html($anime_episodes); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($anime_year)): ?>
                                <div class="stat-item">
                                    <div class="stat-label">السنة</div>
                                    <div class="stat-value"><?php echo esc_html($anime_year); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                </div>

            </section>

            <!-- التفاصيل الكاملة -->
            <?php
            // جمع جميع البيانات
            $anime_type = get_post_meta(get_the_ID(), '_anime_type', true);
            $anime_duration = get_post_meta(get_the_ID(), '_anime_duration', true);
            $anime_status = get_post_meta(get_the_ID(), '_anime_status', true);
            $anime_episodes = get_post_meta(get_the_ID(), '_anime_episodes', true);
            $anime_season = get_post_meta(get_the_ID(), '_anime_season', true);
            $anime_year = get_post_meta(get_the_ID(), '_anime_year', true);
            $anime_links = get_post_meta(get_the_ID(), '_anime_links', true);

            // التحقق من وجود بيانات تفصيلية
            $has_details = !empty($anime_duration) || !empty($anime_season) ||
                          (!empty($anime_links) && is_array($anime_links));

            if ($has_details): ?>
                <section class="anime-details">
                    <h2 class="details-title">
                        <span>📋</span>
                        تفاصيل إضافية
                    </h2>

                    <div class="details-grid">

                        <?php if (!empty($anime_duration)): ?>
                            <div class="detail-card">
                                <span class="detail-icon">⏱️</span>
                                <div class="detail-label">مدة الحلقة</div>
                                <div class="detail-value"><?php echo esc_html($anime_duration); ?> دقيقة</div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($anime_season)): ?>
                            <div class="detail-card">
                                <span class="detail-icon">🗓️</span>
                                <div class="detail-label">الموسم</div>
                                <div class="detail-value">
                                    <?php
                                    if (!empty($anime_season) && !empty($anime_year)) {
                                        echo esc_html($anime_season . ' ' . $anime_year);
                                    } elseif (!empty($anime_season)) {
                                        echo esc_html($anime_season);
                                    }
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>

                    </div>

                    <!-- الروابط الخارجية -->
                    <?php if (!empty($anime_links) && is_array($anime_links)): ?>
                        <div class="anime-links">
                            <h3 class="links-title">
                                <span>🔗</span>
                                روابط مفيدة
                            </h3>
                            <div class="links-grid">
                                <?php foreach ($anime_links as $link): ?>
                                    <?php if (!empty($link['url'])): ?>
                                        <a href="<?php echo esc_url($link['url']); ?>"
                                           target="_blank"
                                           rel="noopener noreferrer"
                                           class="external-link">
                                            <span class="link-icon">🌐</span>
                                            <span class="link-text">
                                                <?php echo esc_html(!empty($link['title']) ? $link['title'] : 'رابط خارجي'); ?>
                                            </span>
                                            <span class="link-arrow">↗</span>
                                        </a>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                </section>
            <?php endif; ?>

            <!-- محتوى القصة -->
            <?php if (get_the_content()): ?>
                <section class="anime-story">
                    <h2 class="story-title">
                        <span>📖</span>
                        قصة الأنمي
                    </h2>
                    <div class="story-content">
                        <?php the_content(); ?>
                    </div>
                </section>
            <?php endif; ?>

        <?php endwhile; ?>

    </div>
</main>

<?php get_sidebar(); ?>
<?php get_footer(); ?>
