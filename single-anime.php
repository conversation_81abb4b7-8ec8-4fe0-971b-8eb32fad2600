<?php
/**
 * قالب مخصص لعرض صفحات الأنمي المفردة - تصميم Bootstrap 5 حديث
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 */
?>

<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

<style>
:root {
  /* ألوان مخصصة للأنمي */
  --anime-primary: #6366f1;
  --anime-secondary: #8b5cf6;
  --anime-accent: #06b6d4;
  --anime-success: #10b981;
  --anime-warning: #f59e0b;
  --anime-danger: #ef4444;

  /* تدرجات لونية */
  --anime-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --anime-gradient-alt: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);

  /* خطوط */
  --anime-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --anime-font-secondary: 'Poppins', sans-serif;
}

/* دعم الوضع الداكن */
[data-bs-theme="dark"] {
  --bs-body-bg: #0d1117;
  --bs-body-color: #e6edf3;
  --bs-card-bg: #161b22;
  --bs-border-color: #30363d;
  --anime-card-bg: #21262d;
  --anime-text-muted: #8b949e;
}

[data-bs-theme="light"] {
  --anime-card-bg: #ffffff;
  --anime-text-muted: #6c757d;
}

/* تصميم عام للصفحة */
body {
  font-family: var(--anime-font-primary);
  background: var(--bs-body-bg);
  transition: all 0.3s ease;
}

/* بطاقة الأنمي الرئيسية */
.anime-hero-card {
  background: var(--anime-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.anime-hero-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* صورة الغلاف */
.anime-cover-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
}

.anime-cover {
  width: 100%;
  height: 500px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.anime-cover:hover {
  transform: scale(1.05);
}

.anime-cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.anime-cover-wrapper:hover .anime-cover-overlay {
  opacity: 1;
}

/* عنوان الأنمي */
.anime-title {
  font-family: var(--anime-font-secondary);
  font-weight: 800;
  background: var(--anime-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  margin-bottom: 1rem;
}

/* تقييم الأنمي */
.anime-rating {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 193, 7, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  margin-bottom: 1rem;
}

.rating-stars {
  color: #ffc107;
  font-size: 1.1rem;
}

.rating-value {
  font-weight: 600;
  color: var(--anime-text-muted);
}

/* ملخص الأنمي */
.anime-summary {
  background: rgba(99, 102, 241, 0.1);
  border-left: 4px solid var(--anime-primary);
  padding: 1rem;
  border-radius: 0.5rem;
  font-style: italic;
  margin-bottom: 1.5rem;
}

/* بطاقات الإحصائيات */
.stat-card {
  background: var(--anime-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
}

.stat-card:hover {
  background: var(--anime-primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.stat-label {
  font-size: 0.875rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.05em;
  opacity: 0.7;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
}

/* بطاقات التفاصيل */
.detail-card {
  background: var(--anime-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  height: 100%;
}

.detail-card:hover {
  border-color: var(--anime-primary);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.2);
}

.detail-icon {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--anime-primary);
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--anime-text-muted);
  margin-bottom: 0.5rem;
}

.detail-value {
  font-size: 1.1rem;
  font-weight: 600;
}

/* روابط خارجية */
.external-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: var(--anime-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.external-link:hover {
  background: var(--anime-primary);
  color: white;
  border-color: var(--anime-primary);
  transform: translateY(-2px);
  text-decoration: none;
}

.link-icon {
  font-size: 1.25rem;
}

.link-arrow {
  transition: all 0.3s ease;
}

.external-link:hover .link-arrow {
  transform: translate(3px, -3px);
}

/* محتوى القصة */
.story-content {
  background: var(--anime-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 1rem;
  padding: 2rem;
}

.story-title {
  font-family: var(--anime-font-secondary);
  font-weight: 700;
  color: var(--anime-primary);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.story-text {
  line-height: 1.8;
  font-size: 1.1rem;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .anime-cover {
    height: 400px;
  }

  .anime-title {
    font-size: 1.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .detail-card {
    padding: 1rem;
  }
}

/* تأثيرات إضافية */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}


/* تقييم الأنمي */
.anime-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--light-color);
  border-radius: var(--border-radius);
}

.rating-stars {
  color: #ffd700;
  font-size: 1.2rem;
}

.rating-text {
  font-weight: 600;
  color: var(--text-color);
}

/* ملخص سريع */
.anime-quick-summary {
  color: var(--text-light);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-style: italic;
}

/* المعلومات السريعة */
.anime-quick-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.quick-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--light-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.quick-info-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.info-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
}

/* قسم التفاصيل العصري */
.modern-details-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-lg);
  padding: 3rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.modern-details-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.anime-details-grid-custom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.modern-detail-item {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.modern-detail-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--secondary-gradient);
  transform: translateX(-100%);
  transition: var(--transition);
}

.modern-detail-item:hover::before {
  transform: translateX(0);
}

.modern-detail-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--box-shadow-lg);
  border-color: rgba(102, 126, 234, 0.2);
}

.modern-detail-icon {
  font-size: 1.5rem;
  margin-left: 0.75rem;
  filter: grayscale(0);
  transition: var(--transition);
}

.modern-detail-item:hover .modern-detail-icon {
  transform: scale(1.2) rotate(5deg);
}

.modern-detail-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0.5rem;
}

.modern-detail-value {
  font-weight: 500;
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* الروابط الخارجية بتصميم عصري */
.anime-links-section-custom {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid rgba(102, 126, 234, 0.1);
}

.anime-links-title-custom {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.anime-links-grid-custom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.anime-external-link-custom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.anime-external-link-custom::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--success-gradient);
  transition: var(--transition);
  z-index: -1;
}

.anime-external-link-custom:hover::before {
  left: 0;
}

.anime-external-link-custom:hover {
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
  border-color: rgba(79, 172, 254, 0.3);
}

.anime-link-icon-custom {
  font-size: 1.2rem;
  margin-left: 0.5rem;
  transition: var(--transition);
}

.anime-external-link-custom:hover .anime-link-icon-custom {
  transform: scale(1.2);
}

.anime-link-text-custom {
  flex: 1;
  text-align: center;
  font-weight: 600;
}

.anime-link-arrow-custom {
  font-size: 1.1rem;
  transition: var(--transition);
}

.anime-external-link-custom:hover .anime-link-arrow-custom {
  transform: translateX(5px) translateY(-5px);
}

/* محتوى القصة بتصميم عصري */
.anime-story-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  padding: 3rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  line-height: 1.8;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.anime-story-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--accent-color);
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

.story-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.story-title {
  font-family: var(--font-secondary);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.story-icon {
  font-size: 1.5rem;
  filter: grayscale(0);
}

.anime-story-content .entry-content {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.8;
}

.anime-story-content .entry-content p {
  margin-bottom: 1.5rem;
}

.anime-story-content .entry-content h1,
.anime-story-content .entry-content h2,
.anime-story-content .entry-content h3 {
  font-family: var(--font-secondary);
  font-weight: 700;
  color: var(--text-color);
  margin: 2rem 0 1rem 0;
}

/* تحسينات للاستجابة */
@media (max-width: 1200px) {
  .modern-anime-container {
    padding: 2rem 1.5rem;
  }

  .anime-header-layout {
    gap: 2rem;
  }

  .modern-hero-image {
    width: 350px;
    max-width: 350px;
  }

  .modern-hero-image img {
    min-height: 450px;
  }
}

@media (max-width: 1024px) {
  .anime-header-layout {
    gap: 2rem;
  }

  .modern-hero-image {
    width: 320px;
    max-width: 320px;
  }

  .modern-hero-image img {
    min-height: 420px;
  }

  .modern-details-section {
    padding: 2rem;
  }

  .anime-details-grid-custom {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .modern-anime-container {
    padding: 1.5rem 1rem;
  }

  .anime-header-layout {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .modern-hero-image {
    width: 300px;
    max-width: 300px;
    margin: 0 auto;
  }

  .modern-hero-image img {
    min-height: 400px;
  }

  .anime-main-info {
    padding: 2rem;
    min-height: auto;
  }

  .modern-main-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
  }

  .anime-quick-info {
    grid-template-columns: repeat(2, 1fr);
  }

  .anime-details-grid-custom {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modern-detail-item {
    padding: 1.5rem;
  }

  .anime-links-grid-custom {
    grid-template-columns: 1fr;
  }

  .anime-story-content {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .modern-anime-container {
    padding: 1rem 0.5rem;
  }

  .anime-header-layout {
    gap: 1.5rem;
  }

  .modern-hero-image {
    width: 250px;
    max-width: 250px;
  }

  .modern-hero-image img {
    min-height: 350px;
  }

  .anime-main-info {
    padding: 1.5rem;
  }

  .anime-quick-info {
    grid-template-columns: 1fr;
  }

  .modern-details-section,
  .anime-story-content {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .modern-detail-item {
    padding: 1rem;
  }

  .anime-external-link-custom {
    padding: 1rem;
  }
}

/* تأثيرات إضافية للتفاعل */
.modern-detail-item,
.anime-external-link-custom,
.anime-story-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للطباعة */
@media print {
  .modern-anime-layout::before {
    display: none;
  }

  .modern-hero-image,
  .modern-details-section,
  .anime-story-content {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .modern-main-title {
    color: #333 !important;
    -webkit-text-fill-color: #333 !important;
  }
}

/* تأثيرات التمرير السلس */
html {
  scroll-behavior: smooth;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #e2e8f0;
    --text-light: #a0aec0;
    --light-color: #2d3748;
    --white: #1a202c;
  }

  .modern-anime-layout {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
}
</style>
<?php

/**
 * تحميل ووردبريس الأساسي
 * نحدد المسار الجذر لووردبريس أولاً
 */
$wp_root_path = realpath(dirname(dirname(dirname(dirname(__FILE__)))));

// التحقق من وجود ملف wp-config.php
$wp_config_path = $wp_root_path . '/wp-config.php';
if (!file_exists($wp_config_path)) {
    die('Error: Unable to locate WordPress configuration file (wp-config.php)');
}

require_once($wp_config_path);

// تعريف ABSPATH إذا لم يكن معرّفاً
if (!defined('ABSPATH')) {
    define('ABSPATH', $wp_root_path . '/');
}

// تحميل ملف wp-settings.php
$wp_settings_path = ABSPATH . 'wp-settings.php';
if (!file_exists($wp_settings_path)) {
    die('Error: Unable to locate WordPress wp-settings.php file');
}

require_once($wp_settings_path);

get_header();

$customizer_settings = tfm_general_settings();
$widget_background = $customizer_settings['tfm_widget_background'] !== get_theme_mod( 'tfm_widget_background', $customizer_settings['tfm_widget_background'] ) ? ' has-background' : '';
$widget_color = $customizer_settings['tfm_widget_color']  !== get_theme_mod( 'tfm_widget_color', $customizer_settings['tfm_widget_color'] ) ? ' has-custom-color' : '';
$widget_link_color = $customizer_settings['tfm_widget_link_color'] !== get_theme_mod( 'tfm_widget_link_color', $customizer_settings['tfm_widget_link_color'] ) ? ' has-custom-link-color' : '';

?>

<!-- تطبيق الوضع الداكن تلقائياً حسب إعدادات النظام -->
<script>
document.documentElement.setAttribute('data-bs-theme',
  window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
);
</script>

<div class="container-fluid py-4" style="background: var(--bs-body-bg);">
    <div class="container">

        <?php while ( have_posts() ) : the_post(); ?>

            <!-- بطاقة الأنمي الرئيسية -->
            <div class="anime-hero-card mb-4 fade-in">
                <div class="row g-0">

                    <!-- صورة الغلاف -->
                    <div class="col-lg-4 col-md-5">
                        <div class="anime-cover-wrapper p-3">
                            <?php if (has_post_thumbnail()): ?>
                                <?php
                                the_post_thumbnail('large', array(
                                    'class' => 'anime-cover',
                                    'alt' => get_the_title(),
                                    'loading' => 'lazy'
                                ));
                                ?>
                                <div class="anime-cover-overlay"></div>
                            <?php else: ?>
                                <div class="anime-cover bg-secondary d-flex align-items-center justify-content-center">
                                    <i class="bi bi-image fs-1 text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- معلومات الأنمي -->
                    <div class="col-lg-8 col-md-7">
                        <div class="p-4">

                            <!-- عنوان الأنمي -->
                            <h1 class="anime-title display-4 mb-3"><?php the_title(); ?></h1>

                            <!-- تقييم الأنمي -->
                            <?php
                            $anime_rating = get_post_meta(get_the_ID(), '_anime_rating', true);
                            if (!empty($anime_rating)):
                            ?>
                                <div class="anime-rating mb-3">
                                    <div class="rating-stars me-2">
                                        <?php
                                        $rating = floatval($anime_rating);
                                        $full_stars = floor($rating);

                                        for ($i = 1; $i <= 5; $i++) {
                                            if ($i <= $full_stars) {
                                                echo '<i class="bi bi-star-fill"></i>';
                                            } else {
                                                echo '<i class="bi bi-star"></i>';
                                            }
                                        }
                                        ?>
                                    </div>
                                    <span class="rating-value"><?php echo esc_html($anime_rating); ?>/5</span>
                                </div>
                            <?php endif; ?>

                            <!-- ملخص سريع -->
                            <?php
                            $anime_summary = get_post_meta(get_the_ID(), '_anime_summary', true);
                            if (!empty($anime_summary)):
                            ?>
                                <div class="anime-summary mb-4">
                                    <i class="bi bi-quote me-2"></i>
                                    <?php echo esc_html($anime_summary); ?>
                                </div>
                            <?php endif; ?>

                            <!-- الإحصائيات السريعة -->
                            <?php
                            $anime_type = get_post_meta(get_the_ID(), '_anime_type', true);
                            $anime_status = get_post_meta(get_the_ID(), '_anime_status', true);
                            $anime_episodes = get_post_meta(get_the_ID(), '_anime_episodes', true);
                            $anime_year = get_post_meta(get_the_ID(), '_anime_year', true);

                            $stats = array();
                            if (!empty($anime_type)) $stats[] = array('icon' => 'bi-film', 'label' => 'النوع', 'value' => $anime_type);
                            if (!empty($anime_status)) $stats[] = array('icon' => 'bi-activity', 'label' => 'الحالة', 'value' => $anime_status);
                            if (!empty($anime_episodes)) $stats[] = array('icon' => 'bi-collection-play', 'label' => 'الحلقات', 'value' => $anime_episodes);
                            if (!empty($anime_year)) $stats[] = array('icon' => 'bi-calendar', 'label' => 'السنة', 'value' => $anime_year);

                            if (!empty($stats)): ?>
                                <div class="row g-3">
                                    <?php foreach ($stats as $stat): ?>
                                        <div class="col-6 col-lg-3">
                                            <div class="stat-card slide-up">
                                                <i class="stat-icon bi <?php echo $stat['icon']; ?>"></i>
                                                <div class="stat-label"><?php echo $stat['label']; ?></div>
                                                <div class="stat-value"><?php echo esc_html($stat['value']); ?></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>

                </div>
            </div>

            <!-- التفاصيل الإضافية -->
            <?php
            $anime_duration = get_post_meta(get_the_ID(), '_anime_duration', true);
            $anime_season = get_post_meta(get_the_ID(), '_anime_season', true);
            $anime_links = get_post_meta(get_the_ID(), '_anime_links', true);

            $details = array();
            if (!empty($anime_duration)) $details[] = array('icon' => 'bi-clock', 'label' => 'مدة الحلقة', 'value' => $anime_duration . ' دقيقة');
            if (!empty($anime_season)) $details[] = array('icon' => 'bi-calendar-event', 'label' => 'الموسم', 'value' => $anime_season . (!empty($anime_year) ? ' ' . $anime_year : ''));

            if (!empty($details) || (!empty($anime_links) && is_array($anime_links))): ?>

                <div class="row mb-4">

                    <!-- التفاصيل الإضافية -->
                    <?php if (!empty($details)): ?>
                        <div class="col-lg-8 mb-4">
                            <h3 class="h4 mb-3 d-flex align-items-center">
                                <i class="bi bi-info-circle me-2 text-primary"></i>
                                تفاصيل إضافية
                            </h3>
                            <div class="row g-3">
                                <?php foreach ($details as $detail): ?>
                                    <div class="col-md-6">
                                        <div class="detail-card slide-up">
                                            <i class="detail-icon <?php echo $detail['icon']; ?>"></i>
                                            <div class="detail-label"><?php echo $detail['label']; ?></div>
                                            <div class="detail-value"><?php echo esc_html($detail['value']); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- الروابط الخارجية -->
                    <?php if (!empty($anime_links) && is_array($anime_links)): ?>
                        <div class="col-lg-4 mb-4">
                            <h3 class="h4 mb-3 d-flex align-items-center">
                                <i class="bi bi-link-45deg me-2 text-primary"></i>
                                روابط مفيدة
                            </h3>
                            <div class="d-grid gap-2">
                                <?php foreach ($anime_links as $link): ?>
                                    <?php if (!empty($link['url'])): ?>
                                        <a href="<?php echo esc_url($link['url']); ?>"
                                           target="_blank"
                                           rel="noopener noreferrer"
                                           class="external-link">
                                            <i class="link-icon bi bi-globe"></i>
                                            <span class="link-text">
                                                <?php echo esc_html(!empty($link['title']) ? $link['title'] : 'رابط خارجي'); ?>
                                            </span>
                                            <i class="link-arrow bi bi-arrow-up-right"></i>
                                        </a>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                </div>

            <?php endif; ?>

            <!-- محتوى القصة -->
            <?php if (get_the_content()): ?>
                <div class="story-content slide-up">
                    <h2 class="story-title">
                        <i class="bi bi-book me-2"></i>
                        قصة الأنمي
                    </h2>
                    <div class="story-text">
                        <?php the_content(); ?>
                    </div>
                </div>
            <?php endif; ?>

        <?php endwhile; ?>

    </div>
</div>

<!-- Bootstrap 5 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- تبديل الوضع الداكن/الفاتح -->
<script>
// إضافة زر تبديل الوضع الداكن
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء زر التبديل
    const themeToggle = document.createElement('button');
    themeToggle.className = 'btn btn-outline-primary position-fixed';
    themeToggle.style.cssText = 'top: 20px; right: 20px; z-index: 1050; border-radius: 50%; width: 50px; height: 50px;';
    themeToggle.innerHTML = '<i class="bi bi-moon-fill"></i>';
    themeToggle.title = 'تبديل الوضع الداكن/الفاتح';

    document.body.appendChild(themeToggle);

    // تحديث أيقونة الزر
    function updateThemeIcon() {
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        themeToggle.innerHTML = currentTheme === 'dark'
            ? '<i class="bi bi-sun-fill"></i>'
            : '<i class="bi bi-moon-fill"></i>';
    }

    // تبديل الوضع
    themeToggle.addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeIcon();
    });

    // تطبيق الوضع المحفوظ
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-bs-theme', savedTheme);
    }

    updateThemeIcon();
});

// تأثيرات الحركة
document.addEventListener('DOMContentLoaded', function() {
    // تأثير fade-in للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // مراقبة العناصر
    document.querySelectorAll('.slide-up').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease-out';
        observer.observe(el);
    });
});
</script>

<?php get_sidebar(); ?>
<?php get_footer(); ?>
