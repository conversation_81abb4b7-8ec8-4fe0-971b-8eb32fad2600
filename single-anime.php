<?php
/**
 * قالب مخصص لعرض صفحات الأنمي المفردة
 * يعرض الصورة بحجم كامل مع تصميم عصري
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 */
?>
<style>
/* استيراد خطوط Google Fonts العصرية */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
  /* ألوان عصرية متدرجة */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  /* ألوان أساسية */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --dark-color: #1a202c;
  --light-color: #f7fafc;
  --text-color: #2d3748;
  --text-light: #718096;
  --white: #ffffff;

  /* تأثيرات بصرية */
  --border-radius: 16px;
  --border-radius-lg: 24px;
  --box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --box-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --backdrop-blur: blur(10px);

  /* خطوط */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Poppins', sans-serif;
}

/* تصميم عام عصري */
.modern-anime-layout {
  font-family: var(--font-primary);
  line-height: 1.7;
  color: var(--text-color);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.modern-anime-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem;
  position: relative;
}

/* تأثيرات الخلفية المتحركة */
.modern-anime-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  z-index: -1;
  animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(-30px) translateY(-50px); }
  66% { transform: translateX(20px) translateY(30px); }
}

/* الصورة الرئيسية بتصميم عصري */
.modern-hero-image {
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow-lg);
  margin-bottom: 3rem;
  background: var(--white);
  padding: 8px;
  transform: perspective(1000px) rotateX(0deg);
  transition: var(--transition);
}

.modern-hero-image:hover {
  transform: perspective(1000px) rotateX(2deg) translateY(-10px);
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

.modern-hero-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  border-radius: var(--border-radius-lg);
  z-index: -1;
}

.modern-hero-image img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: calc(var(--border-radius-lg) - 8px);
  transition: var(--transition);
  filter: brightness(1.05) contrast(1.1);
}

.modern-hero-image:hover img {
  transform: scale(1.02);
  filter: brightness(1.1) contrast(1.15);
}

/* عنوان عصري مع تأثيرات */
.modern-title-overlay {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  padding: 2.5rem;
  border-radius: calc(var(--border-radius-lg) - 8px) calc(var(--border-radius-lg) - 8px) calc(var(--border-radius-lg) - 8px) calc(var(--border-radius-lg) - 8px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-main-title {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: var(--font-secondary);
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 800;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-align: center;
}

/* قسم التفاصيل العصري */
.modern-details-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-lg);
  padding: 3rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.modern-details-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.anime-details-grid-custom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.modern-detail-item {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.modern-detail-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--secondary-gradient);
  transform: translateX(-100%);
  transition: var(--transition);
}

.modern-detail-item:hover::before {
  transform: translateX(0);
}

.modern-detail-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--box-shadow-lg);
  border-color: rgba(102, 126, 234, 0.2);
}

.modern-detail-icon {
  font-size: 1.5rem;
  margin-left: 0.75rem;
  filter: grayscale(0);
  transition: var(--transition);
}

.modern-detail-item:hover .modern-detail-icon {
  transform: scale(1.2) rotate(5deg);
}

.modern-detail-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0.5rem;
}

.modern-detail-value {
  font-weight: 500;
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* الروابط الخارجية بتصميم عصري */
.anime-links-section-custom {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid rgba(102, 126, 234, 0.1);
}

.anime-links-title-custom {
  font-family: var(--font-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.anime-links-grid-custom {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.anime-external-link-custom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius);
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.anime-external-link-custom::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--success-gradient);
  transition: var(--transition);
  z-index: -1;
}

.anime-external-link-custom:hover::before {
  left: 0;
}

.anime-external-link-custom:hover {
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
  border-color: rgba(79, 172, 254, 0.3);
}

.anime-link-icon-custom {
  font-size: 1.2rem;
  margin-left: 0.5rem;
  transition: var(--transition);
}

.anime-external-link-custom:hover .anime-link-icon-custom {
  transform: scale(1.2);
}

.anime-link-text-custom {
  flex: 1;
  text-align: center;
  font-weight: 600;
}

.anime-link-arrow-custom {
  font-size: 1.1rem;
  transition: var(--transition);
}

.anime-external-link-custom:hover .anime-link-arrow-custom {
  transform: translateX(5px) translateY(-5px);
}

/* محتوى القصة بتصميم عصري */
.anime-story-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  padding: 3rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  line-height: 1.8;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.anime-story-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--accent-color);
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

.story-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.story-title {
  font-family: var(--font-secondary);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.story-icon {
  font-size: 1.5rem;
  filter: grayscale(0);
}

.anime-story-content .entry-content {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.8;
}

.anime-story-content .entry-content p {
  margin-bottom: 1.5rem;
}

.anime-story-content .entry-content h1,
.anime-story-content .entry-content h2,
.anime-story-content .entry-content h3 {
  font-family: var(--font-secondary);
  font-weight: 700;
  color: var(--text-color);
  margin: 2rem 0 1rem 0;
}

/* تحسينات للاستجابة */
@media (max-width: 1024px) {
  .modern-anime-container {
    padding: 2rem 1.5rem;
  }

  .modern-details-section {
    padding: 2rem;
  }

  .anime-details-grid-custom {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .modern-anime-container {
    padding: 1.5rem 1rem;
  }

  .modern-main-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .modern-title-overlay {
    padding: 1.5rem;
  }

  .anime-details-grid-custom {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modern-detail-item {
    padding: 1.5rem;
  }

  .anime-links-grid-custom {
    grid-template-columns: 1fr;
  }

  .anime-story-content {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .modern-anime-container {
    padding: 1rem 0.5rem;
  }

  .modern-details-section,
  .anime-story-content {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .modern-detail-item {
    padding: 1rem;
  }

  .anime-external-link-custom {
    padding: 1rem;
  }
}

/* تأثيرات إضافية للتفاعل */
.modern-detail-item,
.anime-external-link-custom,
.anime-story-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للطباعة */
@media print {
  .modern-anime-layout::before {
    display: none;
  }

  .modern-hero-image,
  .modern-details-section,
  .anime-story-content {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .modern-main-title {
    color: #333 !important;
    -webkit-text-fill-color: #333 !important;
  }
}

/* تأثيرات التمرير السلس */
html {
  scroll-behavior: smooth;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #e2e8f0;
    --text-light: #a0aec0;
    --light-color: #2d3748;
    --white: #1a202c;
  }

  .modern-anime-layout {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
}
</style>
<?php

/**
 * تحميل ووردبريس الأساسي
 * نحدد المسار الجذر لووردبريس أولاً
 */
$wp_root_path = realpath(dirname(dirname(dirname(dirname(__FILE__)))));

// التحقق من وجود ملف wp-config.php
$wp_config_path = $wp_root_path . '/wp-config.php';
if (!file_exists($wp_config_path)) {
    die('Error: Unable to locate WordPress configuration file (wp-config.php)');
}

require_once($wp_config_path);

// تعريف ABSPATH إذا لم يكن معرّفاً
if (!defined('ABSPATH')) {
    define('ABSPATH', $wp_root_path . '/');
}

// تحميل ملف wp-settings.php
$wp_settings_path = ABSPATH . 'wp-settings.php';
if (!file_exists($wp_settings_path)) {
    die('Error: Unable to locate WordPress wp-settings.php file');
}

require_once($wp_settings_path);

get_header();

$customizer_settings = tfm_general_settings();
$widget_background = $customizer_settings['tfm_widget_background'] !== get_theme_mod( 'tfm_widget_background', $customizer_settings['tfm_widget_background'] ) ? ' has-background' : '';
$widget_color = $customizer_settings['tfm_widget_color']  !== get_theme_mod( 'tfm_widget_color', $customizer_settings['tfm_widget_color'] ) ? ' has-custom-color' : '';
$widget_link_color = $customizer_settings['tfm_widget_link_color'] !== get_theme_mod( 'tfm_widget_link_color', $customizer_settings['tfm_widget_link_color'] ) ? ' has-custom-link-color' : '';

?>

<main id="main" class="site-main anime-single-page modern-anime-layout">
    <div id="primary" class="content-area the-post anime-post-container modern-anime-container">
        
        <?php while ( have_posts() ) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('anime-single-article modern-anime-article'); ?> data-aos="fade-up">
                
                <!-- الصورة المميزة بحجم كامل مع تأثيرات حديثة -->
                <?php if (has_post_thumbnail()): ?>
                    <div class="anime-hero-image modern-hero-image" data-aos="fade-in">
                        <?php 
                        the_post_thumbnail('full', array(
                            'class' => 'anime-cover-full modern-cover',
                            'alt' => get_the_title(),
                            'loading' => 'lazy'
                        )); 
                        ?>
                        
                        <!-- عنوان الأنمي مع تأثيرات حديثة -->
                        <div class="anime-title-overlay modern-title-overlay">
                            <div class="anime-title-container modern-title-container">
                                <h1 class="anime-main-title modern-main-title" data-aos="fade-up" data-aos-delay="300">
                                    <?php the_title(); ?>
                                </h1>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- محتوى الأنمي -->
                <div class="anime-content-wrapper">
                    
                    <!-- تفاصيل الأنمي بتصميم عصري -->
                    <?php
                    // عرض تفاصيل الأنمي
                    $anime_type = get_post_meta(get_the_ID(), '_anime_type', true);
                    $anime_duration = get_post_meta(get_the_ID(), '_anime_duration', true);
                    $anime_status = get_post_meta(get_the_ID(), '_anime_status', true);
                    $anime_episodes = get_post_meta(get_the_ID(), '_anime_episodes', true);
                    $anime_season = get_post_meta(get_the_ID(), '_anime_season', true);
                    $anime_year = get_post_meta(get_the_ID(), '_anime_year', true);
                    $anime_links = get_post_meta(get_the_ID(), '_anime_links', true);
                    
                    // التحقق من وجود بيانات لعرضها
                    $has_data = !empty($anime_type) || !empty($anime_duration) || !empty($anime_status) || 
                                !empty($anime_episodes) || !empty($anime_season) || !empty($anime_year) || 
                                (!empty($anime_links) && is_array($anime_links));
                    
                    if ($has_data): ?>
                        <div class="anime-details-section modern-details-section" data-aos="fade-up">
                            <div class="anime-details-grid-custom">
                                
                                <?php if (!empty($anime_type)): ?>
                                    <div class="anime-detail-item modern-detail-item" data-aos="fade-up" data-aos-delay="100">
                                        <span class="anime-detail-icon modern-detail-icon">🎬</span>
                                        <span class="anime-detail-label modern-detail-label"><?php _e('النوع:', 'anime-manager'); ?></span>
                                        <span class="anime-detail-value modern-detail-value anime-type-<?php echo esc_attr(strtolower($anime_type)); ?>">
                                            <?php echo esc_html($anime_type); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($anime_duration)): ?>
                                    <div class="anime-detail-item modern-detail-item" data-aos="fade-up" data-aos-delay="200">
                                        <span class="anime-detail-icon modern-detail-icon">⏱️</span>
                                        <span class="anime-detail-label modern-detail-label"><?php _e('مدة الحلقة:', 'anime-manager'); ?></span>
                                        <span class="anime-detail-value modern-detail-value">
                                            <?php echo esc_html($anime_duration); ?> <?php _e('دقيقة', 'anime-manager'); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($anime_status)): ?>
                                    <div class="anime-detail-item modern-detail-item" data-aos="fade-up" data-aos-delay="300">
                                        <span class="anime-detail-icon modern-detail-icon">📊</span>
                                        <span class="anime-detail-label modern-detail-label"><?php _e('الحالة:', 'anime-manager'); ?></span>
                                        <span class="anime-detail-value modern-detail-value anime-status-<?php echo esc_attr(str_replace(' ', '-', strtolower($anime_status))); ?>">
                                            <?php echo esc_html($anime_status); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($anime_episodes)): ?>
                                    <div class="anime-detail-item modern-detail-item" data-aos="fade-up" data-aos-delay="400">
                                        <span class="anime-detail-icon modern-detail-icon">📺</span>
                                        <span class="anime-detail-label modern-detail-label"><?php _e('عدد الحلقات:', 'anime-manager'); ?></span>
                                        <span class="anime-detail-value modern-detail-value">
                                            <?php echo esc_html($anime_episodes); ?> <?php _e('حلقة', 'anime-manager'); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($anime_season) || !empty($anime_year)): ?>
                                    <div class="anime-detail-item modern-detail-item" data-aos="fade-up" data-aos-delay="500">
                                        <span class="anime-detail-icon modern-detail-icon">🗓️</span>
                                        <span class="anime-detail-label modern-detail-label"><?php _e('الموسم:', 'anime-manager'); ?></span>
                                        <span class="anime-detail-value modern-detail-value">
                                            <?php
                                            if (!empty($anime_season) && !empty($anime_year)) {
                                                echo esc_html($anime_season . ' ' . $anime_year);
                                            } elseif (!empty($anime_season)) {
                                                echo esc_html($anime_season);
                                            } elseif (!empty($anime_year)) {
                                                echo esc_html($anime_year);
                                            }
                                            ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                            </div>
                            
                            <!-- الروابط الخارجية -->
                            <?php if (!empty($anime_links) && is_array($anime_links)): ?>
                                <div class="anime-links-section-custom">
                                    <h3 class="anime-links-title-custom">
                                        <span class="anime-detail-icon">🔗</span>
                                        <?php _e('روابط مفيدة', 'anime-manager'); ?>
                                    </h3>
                                    <div class="anime-links-grid-custom">
                                        <?php foreach ($anime_links as $link): ?>
                                            <?php if (!empty($link['url'])): ?>
                                                <a href="<?php echo esc_url($link['url']); ?>" 
                                                   target="_blank" 
                                                   rel="noopener noreferrer" 
                                                   class="anime-external-link-custom">
                                                    <span class="anime-link-icon-custom">🌐</span>
                                                    <span class="anime-link-text-custom">
                                                        <?php echo esc_html(!empty($link['title']) ? $link['title'] : $link['url']); ?>
                                                    </span>
                                                    <span class="anime-link-arrow-custom">↗</span>
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                        </div>
                    <?php endif; ?>
                    
                    <!-- محتوى القصة -->
                    <div class="anime-story-content" data-aos="fade-up" data-aos-delay="600">
                        <div class="story-header">
                            <h2 class="story-title">
                                <span class="story-icon">📖</span>
                                <?php _e('قصة الأنمي', 'anime-manager'); ?>
                            </h2>
                        </div>
                        <div class="entry-content">
                            <?php the_content(); ?>
                        </div>
                    </div>
                    
                </div>
                
                <!-- قسم ما بعد المحتوى -->
                <div class="after-content">
                    <?php
                    if (is_active_sidebar( 'after-content' )) { ?>
                        <div class="sidebar after-content-sidebar<?php echo esc_attr( $widget_background . $widget_color . $widget_link_color ); ?>">
                            <?php dynamic_sidebar( 'after-content'); ?>
                        </div>
                    <?php }
                    
                    // After Content Hook
                    tfm_after_content();
                    
                    // Before Comments Hook
                    tfm_before_comments();
                    
                    // If comments are open or we have at least one comment, load up the comment template.
                    if ( comments_open() || get_comments_number() ) :
                        comments_template();
                    endif;
                    
                    // After Comments Hook
                    tfm_after_comments();
                    ?>
                </div>
                
            </article>
            
        <?php endwhile; ?>
        
    </div>
</main>

<?php get_sidebar(); ?>
<?php get_footer(); ?>
