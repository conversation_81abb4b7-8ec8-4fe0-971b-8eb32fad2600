jQuery(document).ready(function($){

  "use strict";

/*---------------------------------------*/
/* Sticky Header Nav                     */
/*---------------------------------------*/

     var nav = $('.site-header.sticky-nav');
     var body = $('body.has-sticky-nav');
     var siteheader = $('.site-header');
     var navheight = $(".site-header.sticky-nav .site-header-inner").is(':visible') ? $(".site-header.sticky-nav .site-header-inner").outerHeight() : $(".site-header.sticky-nav .mobile-header").outerHeight(); // Primary mav height
     var advertheight = body.hasClass("has-tfm-ad-before-header") ? $(".tfm-before-header.advert").outerHeight() : 0; //advert before header
     var headerheight = $(".site-header.sticky-nav").outerHeight(); // Header height
     var logoheight = headerheight - navheight; 
     var scrolltop = body.hasClass("has-tfm-ad-before-header") ? ( logoheight + advertheight ) : logoheight;
     var marginfix = body.hasClass("has-tfm-ad-before-header") ? ( headerheight + advertheight ) : headerheight;
    
    // Scroll function sticky header

    $(window).on('scroll', function () {
        if ($(this).scrollTop() > scrolltop ) {
            nav.addClass("fixed").css("margin-top", "-" + marginfix + "px" );
            body.addClass("body-fix").css("margin-top", "" + marginfix + "px" );
                $(".tfm-before-header.advert").hide();
        } else {
            nav.removeClass("fixed").css("margin-top", "" );
            body.removeClass("body-fix").css("margin-top", "" );
            $(".tfm-before-header.advert").show();
        }
    });


/*---------------------------------------*/
/* Toggle color mode                  */
/*---------------------------------------*/

   var colormode= $('body').data('color-mode');

  //check for localStorage, add as browser preference if missing
  if (!localStorage.getItem("tfm-color-mode")) {
    if (( window.matchMedia("(prefers-color-scheme: dark)").matches && colormode == 'system' ) || colormode == 'dark' ) {
      localStorage.setItem("tfm-color-mode", "tfm-dark-mode");
    } else {
      localStorage.setItem("tfm-color-mode", "tfm-light-mode");
    }
  }

  //set interface to match localStorage
  if (localStorage.getItem("tfm-color-mode") == "tfm-dark-mode") {
    $("body").addClass("tfm-dark-mode");
    $("body").removeClass("tfm-light-mode");
  } else {
    $("body").removeClass("tfm-dark-mode");
    $("body").addClass("tfm-light-mode");
  }

  //add toggle
  $(".toggle-color-mode").on('click touch', function() {
    if ($("body").hasClass("tfm-dark-mode")) {
      $("body").removeClass("tfm-dark-mode");
      $("body").addClass("tfm-light-mode");
      localStorage.setItem("tfm-color-mode", "tfm-light-mode");
    } else {
      $("body").addClass("tfm-dark-mode");
      $("body").removeClass("tfm-light-mode");
      localStorage.setItem("tfm-color-mode", "tfm-dark-mode");
    }
  });

/*---------------------------------------*/
/* Slide Menu Sidebar                    */
/*---------------------------------------*/

$(".toggle-menu, .toggle-sidebar.mobile-navigation .close-menu, .menu-overlay").on('click touch', function() {
        $(".mobile-navigation").toggleClass("show");
        $(".menu-overlay").fadeToggle(400);
});

// Expand the parent/child menu
$('ul.primary-nav-sidebar li.menu-item-has-children > span.expand').on('click touch', function(event) {
    event.preventDefault();
   $(this).next('.sub-menu').slideToggle(300);
   $(this).next('.sub-menu').toggleClass("visible");
   $(this).toggleClass("close");
});

// Expand the parent/child menu
$('.footer-nav li.menu-item-has-children > a').on('click touch', function(event) {
    event.preventDefault();
   $(this).next('.sub-menu').toggleClass("visible");
   $(this).toggleClass("close");
});
/*---------------------------------------*/
/* Search sidebar                        */
/*---------------------------------------*/

  $(".toggle-search, .site-search .close-menu, .sub-message-404 .toggle-search").on('click touch', function() {
        $(".site-search").toggleClass("show-search");
        $(".site-search").fadeToggle(300);

        // Focus the cursor on the search field when it's visible
        $(".site-search.show-search .search-field").focus();

        // Remove focus when not visible
        $('.site-search:not(.show-search) .search-field').blur();


});

/*---------------------------------------*/
/* ESC key close of open toggle elements */
/*---------------------------------------*/

$(document).keyup(function(e) { 
        if (e.keyCode == 27) { // esc keycode
            if($(".site-search").hasClass("show-search")) {
                $(".site-search").fadeToggle(300);
               $(".site-search").toggleClass("show-search");
            }
            if($('.mobile-navigation').hasClass("show")) {
               $(".mobile-navigation").toggleClass("show");
               $(".menu-overlay").fadeToggle(400);
            }
        }
    });

/*---------------------------------------*/
/* smooth scroll to top                  */
/*---------------------------------------*/
$(".backtotop").on('click touch', function(event){
    event.preventDefault();
    $('body,html').animate({
        scrollTop: 0 ,
        }, 500
    );
});
/*---------------------------------------*/
/* Back to Top Pop Up Link               */
/*---------------------------------------*/
// browser window scroll (in pixels) after which the "back to top" link is shown milliseconds
    var offset = 1200,
        scroll_top_duration = 100,
        $back_to_top = $('.goto-top');

    $(window).on('scroll', function(){
        ( $(this).scrollTop() > offset ) ? $back_to_top.addClass("visible") : $back_to_top.removeClass("visible");
    });
/*---------------------------------------*/
/* smooth scroll anchor links            */
/*---------------------------------------*/
$(document).on('click touch', 'a[href^="#comments"], a[href^="#reviews"]', function (event) {
    event.preventDefault();

    $('html, body').animate({
        scrollTop: $($.attr(this, 'href')).offset().top
    }, 800);
});

/*---------------------------------------*/
/* Toggle comments                       */
/*---------------------------------------*/

$('.toggle-comments').on('click touch', function() {
   $('#comments').slideToggle(400);
   $('#comments').toggleClass("open");
   $(this).toggleClass("close");
});
// Comments anchor link open and reset toggle comments
$('.entry-meta-comment-count a').on('click touch', function() {
   if (! $('#comments').hasClass("open") ) {
    $('#comments').slideToggle(400);
    $('#comments').toggleClass("open");
    $('.toggle-comments').toggleClass("close");
   }
});
$('.comments-pagination a.page-numbers').on('click touch', function() {
   if ($('#comments').hasClass("close") ) {
    $('#comments').slideToggle(400);
    $('#comments').toggleClass("close");
    $('.toggle-comments').toggleClass("open");
   }
});


});

