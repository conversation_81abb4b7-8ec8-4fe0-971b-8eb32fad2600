<?php


/**
 * Mobile display Settings
 *
 * @package WordPress
 * @subpackage jinko
 */

function tfm_customize_register_mobile( $wp_customize ) {

	$customizer_settings = tfm_general_settings();

	// ========================================================
	// Mobile display settings
	// ========================================================

	$wp_customize->add_section( 'tfm_mobile_settings', array(
		'title'    => esc_html__( 'Mobile Display Settings', 'jinko' ),
		'priority' => 130,
		'panel' => 'tfm_theme_settings',
	) );

	// Sidebar
	$wp_customize->add_setting( 'tfm_mobile_hide_sidebar', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_sidebar', array(
		'label'       => esc_html__( 'Hide Sidebar', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Post layout
	$wp_customize->add_setting( 'tfm_mobile_layout', array(
		'default'           => 'grid',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_mobile_layout', array(
		'label'       => esc_html__( 'Post Layout', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'select',
		'choices'     => array(
			'grid' => esc_html__( 'Grid', 'jinko' ),
			'compact' => esc_html__( 'List', 'jinko' ),
		),
	) );

	// Mobile thumbnail
	$wp_customize->add_setting( 'tfm_mobile_thumbnail', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_thumbnail', array(
		'label'       => esc_html__( 'Mobile specific post thumbnails', 'jinko' ),
		'description' => esc_html__('Display mobile versions of post thumbnails on mobile devices', 'jinko'),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// ========================================================
	// Info header
	// ========================================================
    $wp_customize->add_setting('tfm_mobile_toggle_meta_header', array(
        'default'           => '',
        'sanitize_callback' => 'tfm_sanitize_text',
     
    ));
    $wp_customize->add_control(new Tfm_Info_Custom_Control($wp_customize, 'tfm_mobile_toggle_meta_header', array(
        'label'         => esc_html__('Hide meta data on mobile devices', 'jinko'),
        'description'         => esc_html__('These settings apply to homepage, archive, blog and search results', 'jinko'),
        'settings'      => 'tfm_mobile_toggle_meta_header',
        'section'     => 'tfm_mobile_settings',
    )));


	// Excerpt
	$wp_customize->add_setting( 'tfm_mobile_hide_excerpt', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_mobile_hide_excerpt', array(
		'label'       => esc_html__( 'Excerpt', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// read more
	$wp_customize->add_setting( 'tfm_mobile_hide_read_more', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_mobile_hide_read_more', array(
		'label'       => esc_html__( 'Read More', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show author meta
	$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_author', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_author', array(
		'label'       => esc_html__( 'Author', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show author meta
	$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_author_avatar', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_author_avatar', array(
		'label'       => esc_html__( 'Avatar', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show author nickname
	$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_author_nickname', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_author_nickname', array(
		'label'       => esc_html__( 'Nickname', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show category meta
	$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_category', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_category', array(
		'label'       => esc_html__( 'Category', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show read time (TFM theme boost plugin)
	if ( function_exists( 'tfm_read_time') ) {
		$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_read_time', array(
			'default'           => false,
			'sanitize_callback' => 'tfm_sanitize_checkbox',
		) );

		// Control Options
		$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_read_time', array(
			'label'       => esc_html__( 'Read Time', 'jinko' ),
			'section'     => 'tfm_mobile_settings',
			'type'        => 'checkbox',
		) );
	}

	// Show date meta
	$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_comment_count', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_comment_count', array(
		'label'       => esc_html__( 'Comment Count', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show date meta
	$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_date', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_date', array(
		'label'       => esc_html__( 'Date', 'jinko' ),
		'section'     => 'tfm_mobile_settings',
		'type'        => 'checkbox',
	) );

	// Show post views (TFM theme boost plugin)
	if ( function_exists( 'pvc_get_post_views') ) {
		$wp_customize->add_setting( 'tfm_mobile_hide_entry_meta_post_views', array(
			'default'           => false,
			'sanitize_callback' => 'tfm_sanitize_checkbox',
		) );

		// Control Options
		$wp_customize->add_control( 'tfm_mobile_hide_entry_meta_post_views', array(
			'label'       => esc_html__( 'Post Views', 'jinko' ),
			'section'     => 'tfm_mobile_settings',
			'type'        => 'checkbox',
		) );
	}

}

add_action( 'customize_register', 'tfm_customize_register_mobile' );