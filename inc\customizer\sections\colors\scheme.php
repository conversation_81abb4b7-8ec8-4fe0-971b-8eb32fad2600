<?php


/**
 * Default scheme colors
 * light and dark theme
 * $scheme variable
 *
 * @package WordPress
 * @subpackage jinko
 */


// ========================================================
// Primary Colors
// ========================================================

// Theme colour scheme
$wp_customize->add_setting( 'tfm_primary_theme_color' . $scheme, array(
	'default'           => $customizer_settings['primary_theme_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_primary_theme_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Primary Theme Color', 'jinko' ),
) ) );


if ( array_key_exists('secondary_theme_color', $customizer_settings)) {

    $wp_customize->add_setting( 'tfm_secondary_theme_color' . $scheme, array(
		'default'           => $customizer_settings['secondary_theme_color' . $scheme],
		'transport' => 'refresh',
		'sanitize_callback' => 'sanitize_hex_color',
	) );

	$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_secondary_theme_color' . $scheme, array(
      'section' => 'tfm_theme_colors' . $scheme,
      'label'   => esc_html__( 'Secondary Theme Color', 'jinko' ),
    ) ) );

}

if ( array_key_exists('tertiary_theme_color', $customizer_settings)) {
	
    $wp_customize->add_setting( 'tfm_tertiary_theme_color' . $scheme, array(
		'default'           => $customizer_settings['tertiary_theme_color' . $scheme],
		'transport' => 'refresh',
		'sanitize_callback' => 'sanitize_hex_color',
	) );

	$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_tertiary_theme_color' . $scheme, array(
      'section' => 'tfm_theme_colors' . $scheme,
      'label'   => esc_html__( 'Tertiary Theme Color', 'jinko' ),
    ) ) );

}

// ========================================================
// Global Font and Link Colors
// ========================================================

 // Add Setting
$wp_customize->add_setting( 'tfm_body_font_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_body_font_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_body_font_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Body Font Color', 'jinko' ),
) ) );

 // Add Setting
$wp_customize->add_setting( 'tfm_link_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_link_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_link_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Body Link Color', 'jinko' ),
) ) );

$wp_customize->add_setting( 'tfm_link_hover_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_link_hover_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_link_hover_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Body Link Hover Color', 'jinko' ),
) ) );

$wp_customize->add_setting( 'tfm_button_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_button_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_button_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Background', 'jinko' ),
) ) );

$wp_customize->add_setting( 'tfm_button_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_button_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_button_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Color', 'jinko' ),
) ) );

$wp_customize->add_setting( 'tfm_button_hover_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_button_hover_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_button_hover_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Hover Background', 'jinko' ),
) ) );

$wp_customize->add_setting( 'tfm_button_hover_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_button_hover_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_button_hover_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Hover Color', 'jinko' ),
) ) );

// ========================================================
// Theme background
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_body_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_body_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_body_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

// Add Setting
$wp_customize->add_setting( 'tfm_body_background_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_body_background_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_body_background_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Body Background', 'jinko' ),
) ) );

// ========================================================
// Header Colors
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_header_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_header_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_header_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

// Add Setting
$wp_customize->add_setting( 'tfm_header_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_header_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_header_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Header Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_header_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_header_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_header_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Header Color', 'jinko' ),
  'description' => esc_html__( 'Menus and Icons', 'jinko'),
) ) );

    // Add Setting
$wp_customize->add_setting( 'tfm_header_elements_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_header_elements_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_header_elements_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Header Elements Background', 'jinko' ),
) ) );

    // Add Setting
$wp_customize->add_setting( 'tfm_header_elements_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_header_elements_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_header_elements_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Header Elements Color', 'jinko' ),
) ) );

// ========================================================
// Footer Colors
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_footer_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_footer_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_footer_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

// Add Setting
$wp_customize->add_setting( 'tfm_footer_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_footer_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_footer_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Footer Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_footer_font_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_footer_font_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_footer_font_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Footer Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_footer_link_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_footer_link_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_footer_link_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Footer Link Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_footer_button_background' . $scheme, array(
	'default'           => '',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_footer_button_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Footer Button Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_footer_button_color' . $scheme, array(
	'default'           => '',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_footer_button_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Footer Button Color', 'jinko' ),
) ) );

// ========================================================
// Entry Colors
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_entry_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_entry_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_entry_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

// Add Setting
$wp_customize->add_setting( 'tfm_post_background' . $scheme, array(
	'default'  => $customizer_settings['default_post_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Post Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_entry_title_link_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_entry_title_link_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_title_link_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Entry Title Color', 'jinko' ),
) ) );

 // Add Setting
$wp_customize->add_setting( 'tfm_entry_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_entry_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Entry Color (Single)', 'jinko' ),
) ) );

 // Add Setting
$wp_customize->add_setting( 'tfm_entry_link_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_entry_link_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_link_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Entry Link Color (Single)', 'jinko' ),
) ) );

 // Add Setting
$wp_customize->add_setting( 'tfm_entry_link_hover_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_entry_link_hover_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_link_hover_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Entry Link Hover Color (Single)', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_entry_meta_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_entry_meta_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_meta_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Entry Meta Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_entry_meta_link_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_entry_meta_link_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_meta_link_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Entry Meta Link Color', 'jinko' ),
) ) );

// ========================================================
// Button
// ========================================================

// Add Setting
$wp_customize->add_setting( 'tfm_continue_reading_button_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_continue_reading_button_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_continue_reading_button_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_continue_reading_button_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_continue_reading_button_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_continue_reading_button_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_continue_reading_button_hover_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_continue_reading_button_hover_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_continue_reading_button_hover_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Hover Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_continue_reading_button_hover_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_continue_reading_button_hover_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_continue_reading_button_hover_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Button Hover Color', 'jinko' ),
) ) );

// ========================================================
// format icons
// ========================================================

// Add Setting
$wp_customize->add_setting( 'tfm_post_format_icon_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_post_format_icon_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_format_icon_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Post Format Icon Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_post_format_icon_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_post_format_icon_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_format_icon_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Post Format Icon Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_post_format_video_icon_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_post_format_video_icon_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_format_video_icon_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Video Icon Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_post_format_audio_icon_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_post_format_audio_icon_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_format_audio_icon_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Audio Icon Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_post_format_gallery_icon_background' . $scheme, array(
	'default'           => $customizer_settings['tfm_post_format_gallery_icon_background' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_format_gallery_icon_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Gallery Icon Background', 'jinko' ),
) ) );

 // Add Setting
$wp_customize->add_setting( 'tfm_cover_format_primary_color' . $scheme, array(
	'default'           => '#ffffff',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_cover_format_primary_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Cover/Image Format Primary Color', 'jinko' ),
) ) );

// ========================================================
// Single Post/Page Entry Colors
// ========================================================

// If theme supports single color settings

if ( apply_filters( 'tfm_theme_supports_single_color_settings', false )):

	// Separator
	$wp_customize->add_setting('tfm_color_settings_single_entry_separator' . $scheme, array(
		'default'           => '',
		'sanitize_callback' => 'esc_html',
	));
	$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_single_entry_separator' . $scheme, array(
		'settings'		=> 'tfm_color_settings_single_entry_separator' . $scheme,
		'section'  		=> 'tfm_theme_colors' . $scheme,
	)));

	$wp_customize->add_setting('tfm_color_settings_single_entry_info', array(
	    'default'           => '',
	    'sanitize_callback' => 'tfm_sanitize_text',
	 
	));
	$wp_customize->add_control(new Tfm_Info_Custom_Control($wp_customize, 'tfm_color_settings_single_entry_info', array(
	    'label'         => esc_html__('Single Post & Page', 'jinko'),
	    'settings'      => 'tfm_color_settings_single_entry_info',
	    'section'  		=> 'tfm_theme_colors' . $scheme,
	)));

	// Add Setting
	$wp_customize->add_setting( 'tfm_entry_title_link_color_single' . $scheme, array(
		'default'           => '',
		'transport' => 'refresh',
		'sanitize_callback' => 'sanitize_hex_color',
	) );

	// Control Options
	$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_title_link_color_single' . $scheme, array(
	  'section' => 'tfm_theme_colors' . $scheme,
	  'label'   => esc_html__( 'Entry Title Color', 'jinko' ),
	) ) );

	// Add Setting
	$wp_customize->add_setting( 'tfm_excerpt_color_single' . $scheme, array(
		'default'           => '',
		'transport' => 'refresh',
		'sanitize_callback' => 'sanitize_hex_color',
	) );

	// Control Options
	$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_excerpt_color_single' . $scheme, array(
	  'section' => 'tfm_theme_colors' . $scheme,
	  'label'   => esc_html__( 'Excerpt Color', 'jinko' ),
	) ) );

	// Add Setting
	$wp_customize->add_setting( 'tfm_entry_meta_color_single' . $scheme, array(
		'default'           => '',
		'transport' => 'refresh',
		'sanitize_callback' => 'sanitize_hex_color',
	) );

	// Control Options
	$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_meta_color_single' . $scheme, array(
	  'section' => 'tfm_theme_colors' . $scheme,
	  'label'   => esc_html__( 'Entry Meta Color', 'jinko' ),
	) ) );

	// Add Setting
	$wp_customize->add_setting( 'tfm_entry_meta_link_color_single' . $scheme, array(
		'default'           => '',
		'transport' => 'refresh',
		'sanitize_callback' => 'sanitize_hex_color',
	) );

	// Control Options
	$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_entry_meta_link_color_single' . $scheme, array(
	  'section' => 'tfm_theme_colors' . $scheme,
	  'label'   => esc_html__( 'Entry Meta Link Color', 'jinko' ),
	) ) );

endif; 

// ========================================================
// Misc.
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_tags_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_tags_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_tags_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

 // Add Setting
$wp_customize->add_setting( 'tfm_post_tag_background' . $scheme, array(
	'default'           => '',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_tag_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Tag Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_post_tag_color' . $scheme, array(
	'default'           => '',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_post_tag_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Tag Color', 'jinko' ),
) ) );

// ========================================================
// Widgets
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_widgets_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_widgets_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_widgets_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

// Add Setting
$wp_customize->add_setting( 'tfm_widget_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_widget_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_widget_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Widget Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_widget_title_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_widget_title_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_widget_title_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Widget Title Color', 'jinko' ),
) ) );


// Add Setting
$wp_customize->add_setting( 'tfm_widget_link_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_widget_link_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_widget_link_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Widget Link Color', 'jinko' ),
) ) );

// ========================================================
// Misc.
// ========================================================

// Separator
$wp_customize->add_setting('tfm_color_settings_misc_separator' . $scheme, array(
	'default'           => '',
	'sanitize_callback' => 'esc_html',
));
$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_color_settings_misc_separator' . $scheme, array(
	'settings'		=> 'tfm_color_settings_misc_separator' . $scheme,
	'section'  		=> 'tfm_theme_colors' . $scheme,
)));

// Add Setting
$wp_customize->add_setting( 'tfm_cta_background' . $scheme, array(
	'default'           => '',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_cta_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'CTA Button Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_cta_color' . $scheme, array(
	'default'           => $customizer_settings['tfm_cta_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_cta_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'CTA Button Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_cta_background_hover' . $scheme, array(
	'default'           => $customizer_settings['tfm_cta_background_hover' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_cta_background_hover' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'CTA Button Hover Background', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_cta_color_hover' . $scheme, array(
	'default'           => $customizer_settings['tfm_cta_color_hover' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_cta_color_hover' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'CTA Button Hover Color', 'jinko' ),
) ) );

// Add Setting
$wp_customize->add_setting( 'tfm_meta_theme_color' . $scheme, array(
	'default'           => '',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_meta_theme_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Meta Theme Color', 'jinko' ),
) ) );

  // Add Setting
$wp_customize->add_setting( 'tfm_menu_sash_background' . $scheme, array(
	'default'           => $customizer_settings['primary_theme_color' . $scheme],
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_menu_sash_background' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Menu Sash Background', 'jinko' ),
) ) );

  // Add Setting
$wp_customize->add_setting( 'tfm_menu_sash_color' . $scheme, array(
	'default'           => '#ffffff',
	'transport' => 'refresh',
	'sanitize_callback' => 'sanitize_hex_color',
) );

// Control Options
$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'tfm_menu_sash_color' . $scheme, array(
  'section' => 'tfm_theme_colors' . $scheme,
  'label'   => esc_html__( 'Menu Sash Color', 'jinko' ),
) ) );