<?php


/**
 * General Settings
 *
 * @package WordPress
 * @subpackage jinko
 */


function tfm_customize_register_general( $wp_customize ) {

	$customizer_settings = tfm_general_settings();

	// ========================================================
	// General Theme Settings
	// ========================================================

	$wp_customize->add_section( 'tfm_general_settings', array(
		'title'    => esc_html__( 'General Settings', 'jinko' ),
		'priority' => 130,
		'panel' => 'tfm_theme_settings',
	) );

	//  Theme width

	$wp_customize->add_setting( 'tfm_site_width', array(
		'default'           => $customizer_settings['site_width'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_site_width', array(
		'label'       => esc_html__( 'Site Width', 'jinko'),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['site_width'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
	) );

	//  Sidebar width

	$wp_customize->add_setting( 'tfm_sidebar_width', array(
		'default'           => '',
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_sidebar_width', array(
		'label'       => esc_html__( 'Sidebar Width', 'jinko' ),
		'description' => esc_html__( 'Leave blank or set to 0 to use theme defaults:', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
	) );

	//  Single content max width

	$wp_customize->add_setting( 'tfm_content_max_width', array(
		'default'           => $customizer_settings['content_max_width'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_content_max_width', array(
		'label'       => esc_html__( 'Content Width', 'jinko' ),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['content_max_width'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'max' => $customizer_settings['site_width'],
		    ),
	) );

	// Blog list max width

	$wp_customize->add_setting( 'tfm_no_sidebar_blog_max_width', array(
		'default'           => $customizer_settings['no_sidebar_blog_max_width'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_no_sidebar_blog_max_width', array(
		'label'       => esc_html__( 'Blog List Width without Sidebar', 'jinko' ),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['no_sidebar_blog_max_width'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'max' => $customizer_settings['site_width'],
		    ),
	) );

	 $wp_customize->add_setting('tfm_no_sidebar_blog_max_width_checkbox_header', array(
        'default'           => '',
        'sanitize_callback' => 'tfm_sanitize_text',
     
    ));
    $wp_customize->add_control(new tfm_Info_Custom_Control($wp_customize, 'tfm_no_sidebar_blog_max_width_checkbox_header', array(
        'label'         => esc_html__('Apply blog list max width to:', 'jinko'),
        'settings'      => 'tfm_no_sidebar_blog_max_width_checkbox_header',
        'section'       => 'tfm_general_settings',
    )));

	$wp_customize->add_setting( 'tfm_no_sidebar_blog_max_width_cols1', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_no_sidebar_blog_max_width_cols1', array(
		'label'       => esc_html__( '1 Column & List', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_no_sidebar_blog_max_width_cols2', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_no_sidebar_blog_max_width_cols2', array(
		'label'       => esc_html__( '2 Column', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_no_sidebar_blog_max_width_cols3', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_no_sidebar_blog_max_width_cols3', array(
		'label'       => esc_html__( '3 Column', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_no_sidebar_blog_max_width_cols4', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_no_sidebar_blog_max_width_cols4', array(
		'label'       => esc_html__( '4 Column', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_default_border_radius', array(
		'default'           => $customizer_settings['default_border_radius'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_default_border_radius', array(
		'label'       => esc_html__( 'Default Border Radius', 'jinko' ),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['default_border_radius'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'min'   => 0,
		        'step' => 1,
		    ),
	) );

	$wp_customize->add_setting( 'tfm_thumbnail_border_radius', array(
		'default'           => $customizer_settings['thumbnail_border_radius'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_thumbnail_border_radius', array(
		'label'       => esc_html__( 'Post Thumbnail Border Radius', 'jinko' ),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['thumbnail_border_radius'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'min'   => 0,
		        'step' => 1,
		    ),
	) );

	$wp_customize->add_setting( 'tfm_input_border_radius', array(
		'default'           => $customizer_settings['input_border_radius'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_input_border_radius', array(
		'label'       => esc_html__( 'Input Field Border Radius', 'jinko' ),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['input_border_radius'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'min'   => 0,
		        'step' => 1,
		    ),
	) );

	$wp_customize->add_setting( 'tfm_button_border_radius', array(
		'default'           => $customizer_settings['button_border_radius'],
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_button_border_radius', array(
		'label'       => esc_html__( 'Button Border Radius', 'jinko' ),
		'description' => esc_html__( 'Default:', 'jinko' ) . ' ' . $customizer_settings['button_border_radius'],
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'min'   => 0,
		        'step' => 1,
		    ),
	) );

	$wp_customize->add_setting( 'tfm_archive_cat_slug_num', array(
		'default'           => '1',
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_archive_cat_slug_num', array(
		'label'       => esc_html__( 'Number of Category Tags In Archive displays', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
	        'min'   => 1,
	        'max'   => 999,
	    ),
	) );

	$wp_customize->add_setting( 'tfm_excerpt_length', array(
		'default'           => '30',
		'sanitize_callback' => 'absint',
	) );

	$wp_customize->add_control( 'tfm_excerpt_length', array(
		'label'       => esc_html__( 'Excerpt Length', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'number',
		'input_attrs' => array(
		        'min'   => 0,
		    ),
	) );

	// Local fonts
	// $wp_customize->add_setting( 'tfm_local_fonts', array(
	// 	'default'           => false,
	// 	'sanitize_callback' => 'tfm_sanitize_checkbox',
	// ) );

	// // Control Options
	// $wp_customize->add_control( 'tfm_local_fonts', array(
	// 	'label'       => esc_html__( 'Host fonts locally', 'jinko' ),
	// 	'section'     => 'tfm_general_settings',
	// 	'type'        => 'checkbox',
	// ) );

	// Archive lebel
	// $wp_customize->add_setting( 'tfm_remove_archive_title_label', array(
	// 	'default'           => true,
	// 	'sanitize_callback' => 'tfm_sanitize_checkbox',
	// ) );

	// // Control Options
	// $wp_customize->add_control( 'tfm_remove_archive_title_label', array(
	// 	'label'       => esc_html__( 'Remove Archive Title Label', 'jinko' ),
	// 	'section'     => 'tfm_general_settings',
	// 	'type'        => 'checkbox',
	// ) );

	// ========================================================
	// Logo uploads
	// ========================================================

	$wp_customize->add_setting('tfm_logo_upload_dark', array(
		'default'           => '',
		'sanitize_callback' => 'tfm_sanitize_image',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'tfm_logo_upload_dark', array(
               'label'      => esc_html__( 'Logo - Dark Theme', 'jinko' ),
               'section'    => 'title_tagline',
               'priority' => 100,
           )
       )
   );

	$wp_customize->add_setting('tfm_mobile_logo_upload', array(
		'default'           => '',
		'sanitize_callback' => 'tfm_sanitize_image',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'tfm_mobile_logo_upload', array(
               'label'      => esc_html__( 'Mobile Logo', 'jinko' ),
               'section'    => 'title_tagline',
               'priority' => 100,
           )
       )
   );

	$wp_customize->add_setting('tfm_mobile_logo_upload_dark', array(
		'default'           => '',
		'sanitize_callback' => 'tfm_sanitize_image',
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'tfm_mobile_logo_upload_dark', array(
               'label'      => esc_html__( 'Mobile Logo - Dark Theme', 'jinko' ),
               'section'    => 'title_tagline',
               'priority' => 100,
           )
       )
   );

	// Static sisebar sticky
	$wp_customize->add_setting( 'tfm_static_sidebar_sticky', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_static_sidebar_sticky', array(
		'label'       => esc_html__( 'Make Static Sidebar Sticky', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	// Toggle post format icons
	$wp_customize->add_setting( 'tfm_post_format_icons', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_post_format_icons', array(
		'label'       => esc_html__( 'Show Post Format Icons', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	// Toggle Gto Top
	$wp_customize->add_setting( 'tfm_goto_top', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_goto_top', array(
		'label'       => esc_html__( 'Show Back To Top On Scroll', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	// Pagination show Numbers
	$wp_customize->add_setting( 'tfm_pagination_numbers', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_pagination_numbers', array(
		'label'       => esc_html__( 'Show Pagination Numbers', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	// Pagination show Numbers
	$wp_customize->add_setting( 'tfm_pagination_prev_next', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_pagination_prev_next', array(
		'label'       => esc_html__( 'Show Pagination Prev/Next', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_pagination_prev_next_text', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_pagination_prev_next_text', array(
		'label'       => esc_html__( 'Show Pagination Button Text', 'jinko' ),
		'section'     => 'tfm_general_settings',
		'type'        => 'checkbox',
	) );

	// ========================================================
	// Add options to WP Background Image Section
	// ========================================================

	$wp_customize->add_setting( 'tfm_background_image_single', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_background_image_single', array(
		'label'       => esc_html__( 'Show on single post/page', 'jinko'),
		'section'     => 'background_image',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_background_image_archive', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_background_image_archive', array(
		'label'       => esc_html__( 'Show on archive pages', 'jinko'),
		'section'     => 'background_image',
		'type'        => 'checkbox',
	) );



}

add_action( 'customize_register', 'tfm_customize_register_general' );