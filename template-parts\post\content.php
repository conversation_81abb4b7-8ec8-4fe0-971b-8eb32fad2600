<?php
/**
 * Template part for displaying posts
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.1
 */

?>

<?php 

$count = ( ! is_single( ) ? $args['count'] : false );
$tfm_vars = tfm_template_vars( 'content', array( 'count' => $count ));

?>

<article id="post-<?php the_ID(); ?>" <?php post_class( ); ?>>

	<div class="post-inner">

	<?php 

	tfm_post_inner_open();

	// ========================================================
	// Open single cover wrapper
	// ========================================================

	/**
	 * Wraps the featured image and entry header
	 */

	if ( $tfm_vars['content']['cover_wrapper'] ) : ?>

		<div class="cover-wrapper single-cover-wrapper" data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">


	<?php endif;

	// ========================================================
	// Featured media video/audio/gallery
	// ========================================================
	if ( ! $tfm_vars['content']['hero'] ) :

	echo tfm_featured_video();
	tfm_featured_audio();

	endif;

	// ========================================================
	// Post thumbnail (all layouts style) single and archive
	// ========================================================

	 if ( $tfm_vars['content']['post_thumbnail'] ) : ?>

		<div class="thumbnail-wrapper" data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">

			<?php get_template_part( 'template-parts/post/sticky-formats' ); ?>

			<?php tfm_inside_thumbnail_wrapper(); ?>

			<figure class="post-thumbnail<?php echo esc_attr( $tfm_vars['content']['figcaption'] . $tfm_vars['content']['mobile_thumbnail'] ); ?>">

				<?php if ( ! is_single() ) : ?>

				<a href="<?php the_permalink(); ?>">

				<?php endif; ?>

				<?php tfm_get_post_thumbnail( $size = '', $aspect_ratio = $tfm_vars['content']['thumbnail_aspect_ratio'], $count = $count ); ?>

				<?php if ( ! is_single() ) : ?>

				</a>

				<?php endif; ?>
				
			</figure>

			<?php if ( $tfm_vars['content']['figcaption'] ) : // Figcaption ?>

				<figcaption class="featured-media-caption"><?php echo esc_html( get_the_post_thumbnail_caption() ); ?></figcaption>

			<?php endif; ?>

		</div>
		
	<?php endif; 

	// ========================================================
	// Open cover & list layout content wrapper (archive)
	// ========================================================

	if ( $tfm_vars['content']['entry_wrapper'] ) : ?>

	<div class="entry-wrapper">

	<?php

	endif;

	// ========================================================
	// Single entry header, meta and entry title
	// ========================================================

	if ( $tfm_vars['content']['single_entry_header'] ) : ?>

		<header class="entry-header single-entry-header">

	<?php endif;

	// Only show entry meta and title if archive, single or hero-default layout
	if ( ( ! is_single() && ! is_page() ) || (( is_single() || is_page() ) && $tfm_vars['content']['single_entry_header'] ) ) :

		// ========================================================
		// avatar, author, date, post views
		// ========================================================

		$avatar = tfm_toggle_entry_meta('author_avatar') ? 'author_avatar' : '';
		$author = tfm_toggle_entry_meta( 'author') ? 'author' : '';
		$nickname = tfm_toggle_entry_meta( 'author_nickname') ? 'author_nickname' : '';
		$post_views = ! is_single() && 'cover' !== $tfm_vars['content']['post_style'] && tfm_toggle_entry_meta( 'post_views') ? 'post_views' : '';
		$date = '';

		// pass date for selected configurations
		if ( tfm_toggle_entry_meta( 'date' ) ) {
			// with avatar
			if ( $avatar && ( ( ! $author && $nickname ) || ( ! $nickname && $author ) ) ) {
					$date = 'date';
			}
			if ( $avatar && ! $author && ! $nickname ) {
				if ( ! $post_views ) {
					$date = 'date';
				}
			}
			// no avatar
			if ( ! $avatar && ( ! $author || ! $nickname )) {
				if ( ! $post_views || ( $post_views && ! $author && ! $nickname) ) {
					$date = 'date';
				}
			}
		}

		// Pass true/false var to override default (with avatar) multi line
		$is_multi_line = $avatar && ( ( $author && $nickname ) || ( $author && $date ) || ( $nickname && $date ) ) ? true : false;

		$primary_meta_date = $date ? '' : 'date';

		tfm_get_entry_meta( $meta_data = array($avatar, $author, $nickname, $date, $post_views), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = 'author-meta', $meta_class = '', $cats_wrapper = false, $multi_line = $is_multi_line );

		// ========================================================
		// entry title
		// ========================================================

		tfm_before_entry_title(); // hook

		if ( $tfm_vars['content']['entry_title'] ) {
			if ( is_single()) {
				the_title( '<h1 class="entry-title single-entry-title">', '</h1>' );
			} else {
				the_title( '<h3 class="entry-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h3>' );
			}
		}

		tfm_after_entry_title(); // hook

		// ========================================================
		// post format icons (no thumbnail)
		// ========================================================

		if ( ! $tfm_vars['content']['post_thumbnail'] && ! tfm_featured_video( true ) && ! tfm_featured_audio( true ) ) :

			get_template_part( 'template-parts/post/sticky-formats' );

		endif;

		// ========================================================
		// comment count, read time, date, post views (archive)
		// ========================================================

		$comment_count = tfm_toggle_entry_meta('comment_count') ? 'comment_count' : '';
		$read_time = tfm_toggle_entry_meta('read_more') && tfm_toggle_entry_meta( 'read_time') || ( is_single() && tfm_toggle_entry_meta( 'read_time') ) ? 'read_time' : '';
		$date = $primary_meta_date;

		$post_views = tfm_toggle_entry_meta( 'post_views') ? 'post_views' : '';

			tfm_get_entry_meta( $meta_data = array($date, $comment_count, $read_time, $post_views ), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = '', $meta_class = '' );


		// ========================================================
		// Excerpt (
		// ========================================================

		tfm_get_excerpt( );


		// ========================================================
		// Close single entry header
		// ========================================================

		endif; // endif hero entrey header

		if ( is_single() && $tfm_vars['content']['single_entry_header'] ) : ?>

			</header>

		<?php endif;

		// ========================================================
		// Close single cover wrapper
		// ========================================================

		if ( $tfm_vars['content']['cover_wrapper']) : ?>

			</div>

		<?php endif;

		if ( ! is_single() ) :

			get_template_part( 'template-parts/post/hentry-footer' ); 

		endif;

	// ========================================================
	// Close cover & list layout content wrapper (archive)
	// ========================================================

	if ( $tfm_vars['content']['entry_wrapper'] ) : ?>

	</div>

	<?php endif;

	// ========================================================
	// Single content and hentry footer
	// ========================================================

		if ( is_single() ) : ?>

			<div class="single-content-wrapper">

				<?php tfm_before_single_content(); ?>

				<div class="entry-content">

					<?php

						the_content( );

						tfm_after_the_content();


						// tfm set a prev-next class
						global $page, $numpages;

						$prev_next = $page === $numpages ? 'prev' : 'prev-next';
						if ( $page === 1) {
							$prev_next = 'next';
						}

						wp_link_pages( array(
						'before'      => '<div class="nav-links page-pagination ' . esc_attr($prev_next) . '">',
						'after'       => '</div>',
						'link_before' => '<span class="page-numbers">',
						'link_after'  => '</span>',
						'nextpagelink' => esc_html__( 'Next page', 'jinko'),
						'previouspagelink' => esc_html__( 'Previous page', 'jinko'),
						'next_or_number'   => 'next',
						) );

				    ?>

				</div><!-- .entry-content -->

			</div><!-- .single-content-wrapper -->

		<?php

			// Get post tags and share template
			get_template_part( 'template-parts/post/hentry-footer' ); 

		endif;

		tfm_post_inner_close();

		?>

	</div><!-- .post-inner -->

</article>
