<?php
/**
 * The template for displaying tags and share links in the single post footer
 *
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

$customizer_settings = tfm_general_settings();
$hidden_mobile = get_theme_mod( 'tfm_mobile_hide_read_more', false ) ? ' hidden-mobile' : '';
$hidden_hentry_footer = '';

// read more and category
if ( ( tfm_toggle_entry_meta('read_more') && get_theme_mod( 'tfm_mobile_hide_read_more', false ) ) && ( tfm_toggle_entry_meta( 'category') && get_theme_mod( 'tfm_mobile_hide_entry_meta_category', false ) ) ) {
	$hidden_hentry_footer =  ' hidden-mobile';
}
// read time and category
if ( ! tfm_toggle_entry_meta('read_more') && ( tfm_toggle_entry_meta( 'read_time') && get_theme_mod( 'tfm_mobile_hide_entry_meta_read_time', false )) && ( tfm_toggle_entry_meta( 'category') && get_theme_mod( 'tfm_mobile_hide_entry_meta_category', false ) ) ) {
	$hidden_hentry_footer =  ' hidden-mobile';
}
// read time only
if ( ! tfm_toggle_entry_meta('read_more') && ! tfm_toggle_entry_meta( 'category') && ( tfm_toggle_entry_meta( 'read_time') && get_theme_mod( 'tfm_mobile_hide_entry_meta_read_time', false )) ) {
	$hidden_hentry_footer =  ' hidden-mobile';
}
// category only
if ( ! tfm_toggle_entry_meta('read_more') && ! tfm_toggle_entry_meta( 'read_time') && ( tfm_toggle_entry_meta( 'category') && get_theme_mod( 'tfm_mobile_hide_entry_meta_category', false ) ) ) {
	$hidden_hentry_footer =  ' hidden-mobile';
}
if ( is_single()) {
	$hidden_hentry_footer = '';
}

if ( ! is_single() && ! is_page() && ! tfm_toggle_entry_meta('read_more') && ! tfm_toggle_entry_meta('read_time') && ! tfm_toggle_entry_meta('category')  ) {
	return;
}

?>

<?php tfm_before_hentry_footer(); ?>

<footer class="hentry-footer<?php echo esc_attr( $hidden_hentry_footer ); if ( is_single() || is_page() ) { echo ' single-hentry-footer'; } ?>">
	
	<?php if ( tfm_toggle_entry_meta('read_more')) : ?>

		<a class="read-more<?php echo esc_attr( $hidden_mobile ); ?>" href="<?php the_permalink(); ?>"><span class="string"><?php echo esc_html__('read more', 'jinko'); ?></span><span class="screen-reader-text"><?php echo get_the_title(); ?></span></a>

	<?php endif; ?>

	<?php

	// ========================================================
	// Single tags
	// ========================================================

	if ( is_single() ) :
	
	$post_tags = get_the_tags();

	$tag_background = get_theme_mod( 'tfm_post_tag_background', $customizer_settings['tfm_post_tag_background']) ? ' has-background' : '';
	$has_tag_count = get_theme_mod( 'tfm_single_post_tags_count', true ) ? ' has-tag-count' : '';

	if ( tfm_toggle_entry_meta('category') || tfm_toggle_entry_meta( 'updated_date')) : ?>

	<div class="entry-meta footer-meta">

	<?php

	endif;

	// Category meta
	tfm_get_entry_meta( $meta_data = array('updated_date'), $has_wrapper = false, $has_container = false, $html_style = 'div', $meta_wrapper_class = '', $meta_class = '');

	tfm_get_entry_meta( $meta_data = array('category'), $has_wrapper = false, $has_container = true, $html_style = 'li', $meta_wrapper_class = '', $meta_class = 'categories');

	if ( tfm_toggle_entry_meta('category') || tfm_toggle_entry_meta( 'updated_date')) : ?>

	</div>

	<?php

	endif;

	// post tags
	if ( $post_tags && get_theme_mod( 'tfm_single_post_tags', true )) : ?>

		<div class="entry-meta hentry-footer-meta post-tags">

		<ul class="single-post-tags">

		<?php foreach( $post_tags as $tag ) { ?>
	    	<li><a href="<?php echo get_tag_link($tag->term_id); ?>" aria-label="<?php echo esc_attr( $tag->name ); ?>" class="tag-link-<?php echo esc_attr( $tag->term_id . $tag_background . $has_tag_count ); ?>"><?php echo esc_html( $tag->name ); ?><?php if ( get_theme_mod( 'tfm_single_post_tags_count', true ) ) : ?><span class="tag-link-count"><span><?php echo esc_html( $tag->count ); ?></span></span><?php endif; ?></a></li> 
	    <?php } ?>

	    </ul>

	    </div>

	<?php

	endif;

	endif;

	// Author meta
	if ( ! is_single() ) :

		if ( ! tfm_toggle_entry_meta('read_more') ) :

			tfm_get_entry_meta( $meta_data = array('read_time'), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = 'footer-meta', $meta_class = '' );

		endif;

		tfm_get_entry_meta( $meta_data = array('category', 'args' => array('num' => get_theme_mod('tfm_archive_cat_slug_num', 1))), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = 'category-meta', $meta_class = 'categories' );
		
	endif;

	tfm_hentry_footer_close(); ?>

</footer>

<?php tfm_after_hentry_footer(); ?>