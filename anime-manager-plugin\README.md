# مدير الأنمي - Anime Manager

إضافة ووردبريس احترافية لإدارة الأنمي مع تكامل كامل مع قالب Jinko

## 🌟 المميزات

### ✨ إدارة شاملة للأنمي
- **القصة**: استخدام محرر ووردبريس الكامل مع جميع المميزات
- **صورة الغلاف**: نظام الصور المميزة المدمج في ووردبريس
- **النوع**: قائمة منسدلة (تلفازي، بلوراي، ويب)
- **مدة الحلقة**: حقل رقمي بالدقائق مع التحقق من صحة البيانات
- **الحالة**: قائمة منسدلة (مستمر، متوقف، ملغي، مكتمل، قادم لاحقًا)
- **عدد الحلقات**: حقل رقمي مع التحقق من صحة البيانات
- **الموسم والسنة**: قوائم منسدلة للفصول (شتاء، ربيع، صيف، خريف) + حقل السنة
- **الروابط الخارجية**: نظام متقدم لإضافة روابط متعددة مع إمكانية الحذف والإضافة

### 🎨 تصميم عصري ومتجاوب
- **متوافق مع قالب Jinko**: يستخدم نفس متغيرات CSS والألوان
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- **تأثيرات بصرية**: انتقالات سلسة وتأثيرات تفاعلية
- **ألوان ذكية**: ألوان مختلفة للأنواع والحالات المختلفة

### 🔒 أمان عالي
- **Nonces**: حماية من هجمات CSRF
- **تنظيف البيانات**: جميع البيانات يتم تنظيفها قبل الحفظ
- **التحقق من الصلاحيات**: فقط المستخدمون المخولون يمكنهم التعديل
- **منع الوصول المباشر**: حماية جميع الملفات من الوصول المباشر

### ⚡ أداء محسن
- **تحميل شرطي**: ملفات CSS/JS تُحمل فقط عند الحاجة
- **كود محسن**: كتابة نظيفة وفعالة
- **ذاكرة التخزين المؤقت**: متوافق مع إضافات التخزين المؤقت

## 📦 التثبيت

### الطريقة الأولى: النسخ المباشر
1. انسخ مجلد `anime-manager-plugin` إلى `wp-content/plugins/anime-manager`
2. اذهب إلى لوحة تحكم ووردبريس > الإضافات
3. فعل إضافة "مدير الأنمي - Anime Manager"

### الطريقة الثانية: الرفع عبر لوحة التحكم
1. اضغط مجلد `anime-manager-plugin` إلى ملف ZIP
2. اذهب إلى لوحة التحكم > الإضافات > أضف جديد > رفع إضافة
3. ارفع الملف وفعل الإضافة

## 🚀 الاستخدام

### إضافة أنمي جديد
1. اذهب إلى لوحة التحكم > إدارة الأنمي > إضافة جديد
2. أدخل عنوان الأنمي
3. اكتب القصة في المحرر الرئيسي
4. أضف صورة الغلاف باستخدام "تعيين صورة مميزة"
5. املأ تفاصيل الأنمي في صندوق "تفاصيل الأنمي"
6. أضف الروابط الخارجية حسب الحاجة
7. انشر الأنمي

### إدارة الروابط الخارجية
- **إضافة رابط**: اضغط "إضافة رابط جديد"
- **حذف رابط**: اضغط أيقونة سلة المهملات
- **ترتيب الروابط**: اسحب وأفلت (قريباً)

### اختصارات لوحة المفاتيح
- `Ctrl/Cmd + Shift + L`: إضافة رابط جديد
- `Enter`: الانتقال للحقل التالي في الروابط

## 🎯 العرض في الواجهة الأمامية

### تلقائياً
- تظهر تفاصيل الأنمي تلقائياً فوق المحتوى في صفحات الأنمي المفردة
- التصميم متوافق تماماً مع قالب Jinko

### مخصص
يمكنك التحكم في العرض باستخدام الخطافات:

```php
// إخفاء العرض التلقائي
remove_filter('the_content', array(AnimeManager::get_instance(), 'display_anime_details'));

// عرض مخصص
if (is_singular('anime')) {
    $anime_data = get_post_meta(get_the_ID(), '_anime_type', true);
    // عرض البيانات بطريقتك
}
```

## 🛠️ التخصيص

### تخصيص التصميم
أضف CSS مخصص في ملف `style.css` الخاص بالقالب:

```css
/* تخصيص ألوان البطاقة */
.anime-details-card {
    background: #your-color;
    border-color: #your-border-color;
}

/* تخصيص ألوان الأنواع */
.anime-type-تلفازي {
    color: #your-color;
    background: #your-bg-color;
}
```

### إضافة حقول جديدة
```php
// في functions.php
add_action('add_meta_boxes', 'add_custom_anime_fields');
function add_custom_anime_fields() {
    // أضف حقولك المخصصة
}
```

## 📱 التوافق

### ووردبريس
- **الحد الأدنى**: 5.0
- **مُختبر حتى**: 6.4
- **PHP المطلوب**: 7.4+

### القوالب
- **محسن لـ**: قالب Jinko
- **متوافق مع**: جميع القوالب الحديثة
- **Gutenberg**: مدعوم بالكامل

### المتصفحات
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة

**لا تظهر قائمة الأنمي في لوحة التحكم**
- تأكد من تفعيل الإضافة
- تحقق من صلاحيات المستخدم

**لا تظهر التفاصيل في الواجهة الأمامية**
- تأكد من وجود بيانات في الحقول
- تحقق من عدم تعارض الإضافات الأخرى

**مشاكل في التصميم**
- امسح ذاكرة التخزين المؤقت
- تحقق من تحميل ملفات CSS

### تسجيل الأخطاء
```php
// تفعيل تسجيل الأخطاء في wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 🤝 المساهمة

نرحب بمساهماتكم! يمكنكم:
- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين الكود
- ترجمة الإضافة

## 📄 الترخيص

هذه الإضافة مرخصة تحت رخصة GPL v2 أو أحدث.

## 🆘 الدعم

للحصول على الدعم:
1. تحقق من هذا الدليل أولاً
2. ابحث في المشاكل المعروفة
3. تواصل معنا عبر البريد الإلكتروني

## 📈 التحديثات القادمة

- [ ] نظام التقييمات والمراجعات
- [ ] ربط مع قواعد بيانات الأنمي الخارجية
- [ ] نظام المفضلة
- [ ] إحصائيات متقدمة
- [ ] تصدير/استيراد البيانات
- [ ] API للمطورين

---

**شكراً لاستخدام مدير الأنمي! 🎌**
