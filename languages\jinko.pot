msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: 2024-05-28 10:27+0100\n"
"PO-Revision-Date: 2024-05-28 10:27+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.4.2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: esc_html__;__;esc_attr_e;esc_html_e;html_e;_e;"
"esc_attr_x;_x\n"
"X-Poedit-SearchPath-0: .\n"

#: 404.php:20
msgid "404"
msgstr ""

#: 404.php:25
msgid "Oops!, Page Not Found."
msgstr ""

#: 404.php:27
msgid "Sorry, the requested page could not be found."
msgstr ""

#: 404.php:27
msgid "Try searching?"
msgstr ""

#: 404.php:29
msgid "Go Back Home"
msgstr ""

#: archive.php:68 front-page.php:89 home.php:73 search.php:59
msgid "Newer Posts"
msgstr ""

#: archive.php:69 front-page.php:90 home.php:74 search.php:60
msgid "Older Posts"
msgstr ""

#: comments.php:37
msgid "Leave a Comment"
msgstr ""

#: comments.php:37
msgid "1 Comment"
msgstr ""

#: comments.php:37
msgid "% Comments"
msgstr ""

#: comments.php:57
msgid "Reply"
msgstr ""

#: comments.php:63
msgid "Previous"
msgstr ""

#: comments.php:64
msgid "Next"
msgstr ""

#: comments.php:72
msgid "Comments are closed."
msgstr ""

#: footer.php:114
msgid "back to top"
msgstr ""

#: front-page.php:91
msgid "Posts navigation"
msgstr ""

#: functions.php:77
msgid "Primary Menu"
msgstr ""

#: functions.php:78
msgid "Split Menu Left Items"
msgstr ""

#: functions.php:79
msgid "Split Menu Right Items"
msgstr ""

#: functions.php:80
msgid "Toggle Sidebar Primary Menu"
msgstr ""

#: functions.php:81
msgid "Header Secondary Menu"
msgstr ""

#: functions.php:82
msgid "Footer Menu"
msgstr ""

#: functions.php:123
msgid "Static Sidebar"
msgstr ""

#: functions.php:125
msgid "Add widgets here to appear in your static sidebar"
msgstr ""

#: functions.php:133
msgid "Slide Out Sidebar"
msgstr ""

#: functions.php:135
msgid "Add widgets here to appear in your slide out sidebar"
msgstr ""

#: functions.php:143
msgid "Footer Column 1"
msgstr ""

#: functions.php:145 functions.php:155 functions.php:165 functions.php:175
msgid "Add widgets here to appear in your footer column"
msgstr ""

#: functions.php:153
msgid "Footer Column 2"
msgstr ""

#: functions.php:163
msgid "Footer Column 3"
msgstr ""

#: functions.php:173
msgid "Footer Column 4"
msgstr ""

#: functions.php:185
msgid "Home: Between Posts"
msgstr ""

#: functions.php:187
msgid ""
"Add widgets here to appear between posts on the homepage. Set the position "
"of this sidebar in Appearance > Theme Settings > Homepage Settings"
msgstr ""

#: functions.php:195
msgid "Category: Between Posts"
msgstr ""

#: functions.php:197
msgid ""
"Add widgets here to appear between posts in category pages. Set the position "
"of this sidebar in Appearance > Theme Settings > Archive Settings"
msgstr ""

#: functions.php:204
msgid "Single: After Content"
msgstr ""

#: functions.php:206
msgid "Add widgets here to appear ater single post content"
msgstr ""

#: functions.php:230
msgid "on"
msgstr ""

#: functions.php:526
msgid "Menu"
msgstr ""

#: functions.php:532 searchform.php:20
msgid "Search"
msgstr ""

#: functions.php:581
msgid "Posted in"
msgstr ""

#: functions.php:581
msgid "in"
msgstr ""

#: functions.php:710
msgid "Posted by"
msgstr ""

#: functions.php:712
msgid "by"
msgstr ""

#: functions.php:785
msgid " Comment"
msgstr ""

#: functions.php:785
msgid " Comments"
msgstr ""

#: functions.php:1414 functions.php:1416
msgid "Updated:"
msgstr ""

#: functions.php:1681
msgid "Products"
msgstr ""

#: functions.php:1683 functions.php:1685 functions.php:1687 functions.php:1689
msgid "Articles"
msgstr ""

#: functions.php:1692
msgid "Archive"
msgstr ""

#: functions.php:2382 functions.php:2392
msgid "Posts"
msgstr ""

#: functions.php:2414
msgid "posts"
msgstr ""

#: inc/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr ""

#: inc/class-tgm-plugin-activation.php:335
msgid "Install Plugins"
msgstr ""

#: inc/class-tgm-plugin-activation.php:337
#, php-format
msgid "Installing Plugin: %s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:339
#, php-format
msgid "Updating Plugin: %s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr ""

#: inc/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr ""

#: inc/class-tgm-plugin-activation.php:393
#: inc/class-tgm-plugin-activation.php:912
#: inc/class-tgm-plugin-activation.php:2618
#: inc/class-tgm-plugin-activation.php:3665
msgid "Return to the Dashboard"
msgstr ""

#: inc/class-tgm-plugin-activation.php:394
#: inc/class-tgm-plugin-activation.php:3244
msgid "Plugin activated successfully."
msgstr ""

#: inc/class-tgm-plugin-activation.php:395
msgid "The following plugin was activated successfully:"
msgstr ""

#: inc/class-tgm-plugin-activation.php:397
#, php-format
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#: inc/class-tgm-plugin-activation.php:399
#, php-format
msgid ""
"Plugin not activated. A higher version of %s is needed for this theme. "
"Please update the plugin."
msgstr ""

#: inc/class-tgm-plugin-activation.php:401
#, php-format
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr ""

#: inc/class-tgm-plugin-activation.php:403
msgid ""
"There are one or more required or recommended plugins to install, update or "
"activate."
msgstr ""

#: inc/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr ""

#: inc/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr ""

#: inc/class-tgm-plugin-activation.php:1019
msgid ""
"The remote plugin package does not contain a folder with the desired slug "
"and renaming did not work."
msgstr ""

#: inc/class-tgm-plugin-activation.php:1019
#: inc/class-tgm-plugin-activation.php:1022
msgid ""
"Please contact the plugin provider and ask them to package their plugin "
"according to the WordPress guidelines."
msgstr ""

#: inc/class-tgm-plugin-activation.php:1022
msgid ""
"The remote plugin package consists of more than one file, but the files are "
"not packaged in a folder."
msgstr ""

#: inc/class-tgm-plugin-activation.php:2067
#, php-format
msgid "TGMPA v%s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2358
msgid "Required"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2361
msgid "Recommended"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2377
msgid "WordPress Repository"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2380
msgid "External Source"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2383
msgid "Pre-Packaged"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2400
msgid "Not Installed"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2404
msgid "Installed But Not Activated"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2406
msgid "Active"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2412
msgid "Required Update not Available"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2415
msgid "Requires Update"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2418
msgid "Update recommended"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2427
#, php-format
msgid "%1$s, %2$s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2567
msgid "unknown"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2575
msgid "Installed version:"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2583
msgid "Minimum required version:"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2595
msgid "Available version:"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2618
msgid "No plugins to install, update or activate."
msgstr ""

#: inc/class-tgm-plugin-activation.php:2632
msgid "Plugin"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2633
msgid "Source"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2634
msgid "Type"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2638
msgid "Version"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2639
msgid "Status"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2688
#, php-format
msgid "Install %2$s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2693
#, php-format
msgid "Update %2$s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2699
#, php-format
msgid "Activate %2$s"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2769
msgid "Upgrade message from the plugin author:"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2802
msgid "Install"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2808
msgid "Update"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2811
msgid "Activate"
msgstr ""

#: inc/class-tgm-plugin-activation.php:2842
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: inc/class-tgm-plugin-activation.php:2844
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: inc/class-tgm-plugin-activation.php:2885
msgid "No plugins are available to be installed at this time."
msgstr ""

#: inc/class-tgm-plugin-activation.php:2887
msgid "No plugins are available to be updated at this time."
msgstr ""

#: inc/class-tgm-plugin-activation.php:2993
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3019
msgid "No plugins are available to be activated at this time."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3243
msgid "Plugin activation failed."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3583
#, php-format
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/class-tgm-plugin-activation.php:3586
#, php-format
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3588
#, php-format
msgid "The installation of %1$s failed."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3592
msgid ""
"The installation and activation process is starting. This process may take a "
"while on some hosts, so please be patient."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3594
#, php-format
msgid "%1$s installed and activated successfully."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3594
#: inc/class-tgm-plugin-activation.php:3602
msgid "Show Details"
msgstr ""

#: inc/class-tgm-plugin-activation.php:3594
#: inc/class-tgm-plugin-activation.php:3602
msgid "Hide Details"
msgstr ""

#: inc/class-tgm-plugin-activation.php:3595
msgid "All installations and activations have been completed."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3597
#, php-format
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/class-tgm-plugin-activation.php:3600
msgid ""
"The installation process is starting. This process may take a while on some "
"hosts, so please be patient."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3602
#, php-format
msgid "%1$s installed successfully."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3603
msgid "All installations have been completed."
msgstr ""

#: inc/class-tgm-plugin-activation.php:3605
#, php-format
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/customizer/customizer.php:30
msgid "Jinko: Theme Settings"
msgstr ""

#: inc/customizer/customizer.php:31
msgid "Customize theme settings"
msgstr ""

#: inc/customizer/customizer_colors.php:24
msgid "Jinko: Color Settings"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:16
msgid "Category Tags"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:32
msgid "Default Background"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:46
msgid "Default Color"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:62
msgid "Default Background (Dark theme)"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:76
msgid "Default Color  (Dark theme)"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:102
#: inc/customizer/sections/colors/scheme.php:674
msgid "Tag Background"
msgstr ""

#: inc/customizer/sections/colors/category-tags.php:117
#: inc/customizer/sections/colors/scheme.php:687
msgid "Tag Color"
msgstr ""

#: inc/customizer/sections/colors/color-mode.php:16
msgid "Color Mode"
msgstr ""

#: inc/customizer/sections/colors/color-mode.php:27
msgid "Color Scheme"
msgstr ""

#: inc/customizer/sections/colors/color-mode.php:31
msgid "System Preference"
msgstr ""

#: inc/customizer/sections/colors/color-mode.php:32
msgid "Dark"
msgstr ""

#: inc/customizer/sections/colors/color-mode.php:33
msgid "Light"
msgstr ""

#: inc/customizer/sections/colors/color-mode.php:45
msgid "Show Color Mode Toggle"
msgstr ""

#: inc/customizer/sections/colors/dark.php:18
msgid "Dark Theme"
msgstr ""

#: inc/customizer/sections/colors/light.php:18
msgid "Light Theme"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:27
#: inc/gutenberg_color_palette.php:8
msgid "Primary Theme Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:41
#: inc/gutenberg_color_palette.php:13
msgid "Secondary Theme Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:56
#: inc/gutenberg_color_palette.php:18
msgid "Tertiary Theme Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:75
msgid "Body Font Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:88
msgid "Body Link Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:100
msgid "Body Link Hover Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:112
#: inc/customizer/sections/colors/scheme.php:443
msgid "Button Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:124
#: inc/customizer/sections/colors/scheme.php:456
msgid "Button Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:136
#: inc/customizer/sections/colors/scheme.php:469
msgid "Button Hover Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:148
#: inc/customizer/sections/colors/scheme.php:482
msgid "Button Hover Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:175
msgid "Body Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:202
msgid "Header Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:215
msgid "Header Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:216
msgid "Menus and Icons"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:229
msgid "Header Elements Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:242
msgid "Header Elements Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:269
msgid "Footer Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:282
msgid "Footer Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:295
msgid "Footer Link Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:308
msgid "Footer Button Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:321
msgid "Footer Button Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:348
msgid "Post Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:361
#: inc/customizer/sections/colors/scheme.php:606
msgid "Entry Title Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:374
msgid "Entry Color (Single)"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:387
msgid "Entry Link Color (Single)"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:400
msgid "Entry Link Hover Color (Single)"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:413
#: inc/customizer/sections/colors/scheme.php:632
msgid "Entry Meta Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:426
#: inc/customizer/sections/colors/scheme.php:645
msgid "Entry Meta Link Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:499
msgid "Post Format Icon Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:512
msgid "Post Format Icon Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:525
msgid "Video Icon Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:538
msgid "Audio Icon Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:551
msgid "Gallery Icon Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:564
msgid "Cover/Image Format Primary Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:591
msgid "Single Post & Page"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:619
msgid "Excerpt Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:714
msgid "Widget Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:727
msgid "Widget Title Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:741
msgid "Widget Link Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:768
msgid "CTA Button Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:781
msgid "CTA Button Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:794
msgid "CTA Button Hover Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:807
msgid "CTA Button Hover Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:820
msgid "Meta Theme Color"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:833
msgid "Menu Sash Background"
msgstr ""

#: inc/customizer/sections/colors/scheme.php:846
msgid "Menu Sash Color"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:16
msgid "Archive/Category Settings"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:29
#: inc/customizer/sections/customizer-homepage.php:34
#: inc/customizer/sections/customizer-page.php:29
#: inc/customizer/sections/customizer-single.php:33
msgid "Display Sidebar"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:41
#: inc/customizer/sections/customizer-homepage.php:75
#: inc/customizer/sections/customizer-mobile.php:45
msgid "Post Layout"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:45
#: inc/customizer/sections/customizer-homepage.php:79
msgid "Masonry"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:46
#: inc/customizer/sections/customizer-homepage.php:80
#: inc/customizer/sections/customizer-mobile.php:49
#: inc/customizer/sections/customizer-single.php:306
msgid "Grid"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:47
#: inc/customizer/sections/customizer-homepage.php:81
#: inc/customizer/sections/customizer-mobile.php:50
#: inc/customizer/sections/customizer-single.php:307
msgid "List"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:57
#: inc/customizer/sections/customizer-homepage.php:107
#: inc/customizer/sections/customizer-single.php:61
msgid "Post Style"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:61
#: inc/customizer/sections/customizer-homepage.php:111
#: inc/customizer/sections/customizer-page.php:61
#: inc/customizer/sections/customizer-single.php:65
#: inc/customizer/sections/customizer-single.php:322
msgid "Classic"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:62
#: inc/customizer/sections/customizer-homepage.php:112
#: inc/customizer/sections/customizer-single.php:323
msgid "Card"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:63
#: inc/customizer/sections/customizer-homepage.php:113
#: inc/customizer/sections/customizer-page.php:63
#: inc/customizer/sections/customizer-single.php:67
#: inc/customizer/sections/customizer-single.php:324
msgid "Cover"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:75
#: inc/customizer/sections/customizer-homepage.php:92
msgid "Number of Columns"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:94
#: inc/customizer/sections/customizer-homepage.php:170
#: inc/customizer/sections/customizer-mobile.php:91
msgid "Excerpt"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:106
#: inc/customizer/sections/customizer-homepage.php:182
#: inc/customizer/sections/customizer-image-format.php:54
#: inc/customizer/sections/customizer-mobile.php:103
msgid "Read More"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:119
#: inc/customizer/sections/customizer-homepage.php:195
#: inc/customizer/sections/customizer-image-format.php:67
msgid "Entry Title"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:132
#: inc/customizer/sections/customizer-homepage.php:208
#: inc/customizer/sections/customizer-image-format.php:80
#: inc/customizer/sections/customizer-page.php:108
#: inc/customizer/sections/customizer-single.php:98
msgid "Thumbnail"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:144
#: inc/customizer/sections/customizer-homepage.php:220
#: inc/customizer/sections/customizer-image-format.php:92
#: inc/customizer/sections/customizer-page.php:74
#: inc/customizer/sections/customizer-single.php:78
msgid "Thumbnail Aspect Ratio"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:148
#: inc/customizer/sections/customizer-homepage.php:224
#: inc/customizer/sections/customizer-image-format.php:96
#: inc/customizer/sections/customizer-page.php:78
#: inc/customizer/sections/customizer-single.php:82
msgid "Wide"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:149
#: inc/customizer/sections/customizer-homepage.php:225
#: inc/customizer/sections/customizer-image-format.php:97
#: inc/customizer/sections/customizer-page.php:79
#: inc/customizer/sections/customizer-single.php:83
msgid "Landscape"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:150
#: inc/customizer/sections/customizer-homepage.php:226
#: inc/customizer/sections/customizer-image-format.php:98
#: inc/customizer/sections/customizer-page.php:80
#: inc/customizer/sections/customizer-single.php:84
msgid "Portrait"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:151
#: inc/customizer/sections/customizer-homepage.php:227
#: inc/customizer/sections/customizer-image-format.php:99
#: inc/customizer/sections/customizer-page.php:81
#: inc/customizer/sections/customizer-single.php:85
msgid "Square"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:152
#: inc/customizer/sections/customizer-homepage.php:228
#: inc/customizer/sections/customizer-image-format.php:100
#: inc/customizer/sections/customizer-page.php:83
#: inc/customizer/sections/customizer-single.php:87
msgid "Uncropped"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:164
#: inc/customizer/sections/customizer-homepage.php:240
msgid "Video &amp; Audio"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:177
#: inc/customizer/sections/customizer-homepage.php:253
#: inc/customizer/sections/customizer-image-format.php:112
#: inc/customizer/sections/customizer-single.php:139
msgid "\"by\""
msgstr ""

#: inc/customizer/sections/customizer-archive.php:190
#: inc/customizer/sections/customizer-homepage.php:266
#: inc/customizer/sections/customizer-image-format.php:125
#: inc/customizer/sections/customizer-single.php:152
msgid "\"in\""
msgstr ""

#: inc/customizer/sections/customizer-archive.php:203
#: inc/customizer/sections/customizer-homepage.php:279
#: inc/customizer/sections/customizer-image-format.php:138
#: inc/customizer/sections/customizer-mobile.php:116
#: inc/customizer/sections/customizer-single.php:163
msgid "Author"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:216
#: inc/customizer/sections/customizer-homepage.php:292
#: inc/customizer/sections/customizer-image-format.php:151
#: inc/customizer/sections/customizer-mobile.php:129
#: inc/customizer/sections/customizer-single.php:174
msgid "Avatar"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:229
#: inc/customizer/sections/customizer-homepage.php:305
#: inc/customizer/sections/customizer-image-format.php:164
#: inc/customizer/sections/customizer-mobile.php:142
#: inc/customizer/sections/customizer-single.php:185
msgid "Nickname"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:242
#: inc/customizer/sections/customizer-homepage.php:318
#: inc/customizer/sections/customizer-image-format.php:177
#: inc/customizer/sections/customizer-mobile.php:155
#: inc/customizer/sections/customizer-single.php:196
msgid "Category"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:256
#: inc/customizer/sections/customizer-homepage.php:332
#: inc/customizer/sections/customizer-image-format.php:191
#: inc/customizer/sections/customizer-mobile.php:169
#: inc/customizer/sections/customizer-single.php:231
msgid "Read Time"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:270
#: inc/customizer/sections/customizer-homepage.php:346
#: inc/customizer/sections/customizer-image-format.php:205
#: inc/customizer/sections/customizer-mobile.php:183
#: inc/customizer/sections/customizer-single.php:244
msgid "Comment Count"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:283
#: inc/customizer/sections/customizer-homepage.php:359
#: inc/customizer/sections/customizer-image-format.php:218
#: inc/customizer/sections/customizer-mobile.php:196
#: inc/customizer/sections/customizer-single.php:207
msgid "Date"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:306
#: inc/customizer/sections/customizer-homepage.php:382
msgid "Add widgets between posts"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:307
msgid ""
"Select the position you would like to display your widget(s). Add your "
"widgets in Appearance > Widgets > Category: Between Posts"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:323
#: inc/customizer/sections/customizer-homepage.php:399
msgid "Only show widget on first page"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:348
msgid "Show Archive Title"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:361
msgid "Post Count"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:374
msgid "Description"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:400
msgid "Author Avatar"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:413
msgid "Author Bio"
msgstr ""

#: inc/customizer/sections/customizer-archive.php:426
msgid "Author Nickname"
msgstr ""

#: inc/customizer/sections/customizer-footer.php:16
msgid "Footer Settings"
msgstr ""

#: inc/customizer/sections/customizer-footer.php:29
msgid "Footer Text"
msgstr ""

#: inc/customizer/sections/customizer-footer.php:30
msgid "Basic HTML is supported"
msgstr ""

#: inc/customizer/sections/customizer-general.php:21
msgid "General Settings"
msgstr ""

#: inc/customizer/sections/customizer-general.php:34
msgid "Site Width"
msgstr ""

#: inc/customizer/sections/customizer-general.php:35
#: inc/customizer/sections/customizer-general.php:63
#: inc/customizer/sections/customizer-general.php:80
#: inc/customizer/sections/customizer-general.php:154
#: inc/customizer/sections/customizer-general.php:170
#: inc/customizer/sections/customizer-general.php:186
#: inc/customizer/sections/customizer-general.php:202
msgid "Default:"
msgstr ""

#: inc/customizer/sections/customizer-general.php:48
msgid "Sidebar Width"
msgstr ""

#: inc/customizer/sections/customizer-general.php:49
msgid "Leave blank or set to 0 to use theme defaults:"
msgstr ""

#: inc/customizer/sections/customizer-general.php:62
msgid "Content Width"
msgstr ""

#: inc/customizer/sections/customizer-general.php:79
msgid "Blog List Width without Sidebar"
msgstr ""

#: inc/customizer/sections/customizer-general.php:94
msgid "Apply blog list max width to:"
msgstr ""

#: inc/customizer/sections/customizer-general.php:106
msgid "1 Column & List"
msgstr ""

#: inc/customizer/sections/customizer-general.php:118
msgid "2 Column"
msgstr ""

#: inc/customizer/sections/customizer-general.php:130
msgid "3 Column"
msgstr ""

#: inc/customizer/sections/customizer-general.php:142
msgid "4 Column"
msgstr ""

#: inc/customizer/sections/customizer-general.php:153
msgid "Default Border Radius"
msgstr ""

#: inc/customizer/sections/customizer-general.php:169
msgid "Post Thumbnail Border Radius"
msgstr ""

#: inc/customizer/sections/customizer-general.php:185
msgid "Input Field Border Radius"
msgstr ""

#: inc/customizer/sections/customizer-general.php:201
msgid "Button Border Radius"
msgstr ""

#: inc/customizer/sections/customizer-general.php:217
msgid "Number of Category Tags In Archive displays"
msgstr ""

#: inc/customizer/sections/customizer-general.php:232
msgid "Excerpt Length"
msgstr ""

#: inc/customizer/sections/customizer-general.php:276
msgid "Logo - Dark Theme"
msgstr ""

#: inc/customizer/sections/customizer-general.php:289
msgid "Mobile Logo"
msgstr ""

#: inc/customizer/sections/customizer-general.php:302
msgid "Mobile Logo - Dark Theme"
msgstr ""

#: inc/customizer/sections/customizer-general.php:317
msgid "Make Static Sidebar Sticky"
msgstr ""

#: inc/customizer/sections/customizer-general.php:330
msgid "Show Post Format Icons"
msgstr ""

#: inc/customizer/sections/customizer-general.php:343
msgid "Show Back To Top On Scroll"
msgstr ""

#: inc/customizer/sections/customizer-general.php:356
msgid "Show Pagination Numbers"
msgstr ""

#: inc/customizer/sections/customizer-general.php:369
msgid "Show Pagination Prev/Next"
msgstr ""

#: inc/customizer/sections/customizer-general.php:381
msgid "Show Pagination Button Text"
msgstr ""

#: inc/customizer/sections/customizer-general.php:396
msgid "Show on single post/page"
msgstr ""

#: inc/customizer/sections/customizer-general.php:407
msgid "Show on archive pages"
msgstr ""

#: inc/customizer/sections/customizer-header.php:16
msgid "Header Settings"
msgstr ""

#: inc/customizer/sections/customizer-header.php:27
msgid "Header Layout"
msgstr ""

#: inc/customizer/sections/customizer-header.php:31
msgid "Default"
msgstr ""

#: inc/customizer/sections/customizer-header.php:32
msgid "Logo Center Split Menu"
msgstr ""

#: inc/customizer/sections/customizer-header.php:33
msgid "Logo Left w/Menu"
msgstr ""

#: inc/customizer/sections/customizer-header.php:45
msgid "Full width"
msgstr ""

#: inc/customizer/sections/customizer-header.php:67
msgid "Show Toggle Menu on Desktop"
msgstr ""

#: inc/customizer/sections/customizer-header.php:80
msgid "Show Toggle Menu on Mobile"
msgstr ""

#: inc/customizer/sections/customizer-header.php:93
msgid "Show Toggle Search on Desktop"
msgstr ""

#: inc/customizer/sections/customizer-header.php:106
msgid "Show Toggle Search on Mobile"
msgstr ""

#: inc/customizer/sections/customizer-header.php:119
msgid "Make Header Nav Sticky on Desktop"
msgstr ""

#: inc/customizer/sections/customizer-header.php:132
msgid "Make Header Nav Sticky on Mobile"
msgstr ""

#: inc/customizer/sections/customizer-header.php:145
msgid "Show Site Tagline"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:21
msgid "Homepage Settings"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:47
msgid "Title"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:59
msgid "Subtitle"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:124
msgid "Offset (Number of Posts to Skip)"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:140
msgid "Exclude Post IDs"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:141
msgid ""
"Enter a comma separated List of post IDs you want to exclude from the "
"homepage"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:154
msgid "Exclude Categories"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:155
msgid ""
"Enter a comma separated List of categories (ID or slug) you want to exclude "
"from the homepage"
msgstr ""

#: inc/customizer/sections/customizer-homepage.php:383
msgid ""
"Select the position you would like to display your widget(s). Add your "
"widgets in Appearance > Widgets > Home: Between Posts"
msgstr ""

#: inc/customizer/sections/customizer-image-format.php:16
msgid "Image Format Settings"
msgstr ""

#: inc/customizer/sections/customizer-image-format.php:29
msgid "Use Archive &amp; Homepage Settings"
msgstr ""

#: inc/customizer/sections/customizer-image-format.php:42
msgid "Excerpt (Auto-Generated)"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:16
msgid "Logo Settings"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:30
msgid ""
"Upload and manage your logos in in Appearance > Customize > Site Identity"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:44
msgid "Site title"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:57
msgid "Font Size *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:75
msgid "Large Mobile Font Size *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:93
msgid "Small Mobile Font Size *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:124
msgid "Custom Logo"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:137
msgid "Max Width Desktop *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:155
msgid "Max Width Large Mobile *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:173
msgid "Max Width Small Mobile *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:204
msgid "Toggle Sidebar"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:217
msgid "Site Title Font Size *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:235
msgid "Custom Logo Max Width *px"
msgstr ""

#: inc/customizer/sections/customizer-logo.php:253
msgid "Display Sidebar Logo/Site Title"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:20
msgid "Mobile Display Settings"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:33
msgid "Hide Sidebar"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:62
msgid "Mobile specific post thumbnails"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:63
msgid "Display mobile versions of post thumbnails on mobile devices"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:77
msgid "Hide meta data on mobile devices"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:78
msgid "These settings apply to homepage, archive, blog and search results"
msgstr ""

#: inc/customizer/sections/customizer-mobile.php:210
msgid "Post Views"
msgstr ""

#: inc/customizer/sections/customizer-page.php:16
msgid "Page Settings"
msgstr ""

#: inc/customizer/sections/customizer-page.php:41
#: inc/customizer/sections/customizer-single.php:45
msgid "Sidebar Position"
msgstr ""

#: inc/customizer/sections/customizer-page.php:45
#: inc/customizer/sections/customizer-single.php:49
msgid "Side"
msgstr ""

#: inc/customizer/sections/customizer-page.php:46
#: inc/customizer/sections/customizer-single.php:50
msgid "Below Featured Media"
msgstr ""

#: inc/customizer/sections/customizer-page.php:57
msgid "Page Style"
msgstr ""

#: inc/customizer/sections/customizer-page.php:62
#: inc/customizer/sections/customizer-single.php:66
msgid "Classic Alt."
msgstr ""

#: inc/customizer/sections/customizer-page.php:82
#: inc/customizer/sections/customizer-single.php:86
msgid "Hero"
msgstr ""

#: inc/customizer/sections/customizer-page.php:95
#: inc/customizer/sections/customizer-single.php:112
msgid "Full Width"
msgstr ""

#: inc/customizer/sections/customizer-page.php:119
msgid "Show Page Title on Front Page"
msgstr ""

#: inc/customizer/sections/customizer-page.php:120
msgid "If your Homepage is set to a page"
msgstr ""

#: inc/customizer/sections/customizer-single.php:20
msgid "Single Post Settings"
msgstr ""

#: inc/customizer/sections/customizer-single.php:126
msgid "Display Custom excerpt"
msgstr ""

#: inc/customizer/sections/customizer-single.php:218
msgid "Updated Date"
msgstr ""

#: inc/customizer/sections/customizer-single.php:255
msgid "Post Tags"
msgstr ""

#: inc/customizer/sections/customizer-single.php:266
msgid "Post Tag Count"
msgstr ""

#: inc/customizer/sections/customizer-single.php:290
msgid "Previous/Next Post"
msgstr ""

#: inc/customizer/sections/customizer-single.php:302
msgid "Prev/Next Post Layout"
msgstr ""

#: inc/customizer/sections/customizer-single.php:318
msgid "Prev/Next Post Style"
msgstr ""

#: inc/customizer/sections/customizer-single.php:335
msgid "Prev/Next Entry Title"
msgstr ""

#: inc/customizer/sections/customizer-single.php:348
msgid "Prev/Next Post Thumbnail"
msgstr ""

#: inc/customizer/sections/customizer-single.php:360
msgid "Prev/Next Post Excerpt"
msgstr ""

#: inc/customizer/sections/customizer-single.php:384
msgid "Prev/Next Post Header"
msgstr ""

#: inc/customizer/sections/customizer-single.php:409
msgid "Author Bio Avatar"
msgstr ""

#: inc/customizer/sections/customizer-single.php:422
msgid "Author Bio Name"
msgstr ""

#: inc/customizer/sections/customizer-single.php:435
msgid "Author Bio Info"
msgstr ""

#: inc/customizer/sections/customizer-single.php:448
msgid "Click Button to Open Comments"
msgstr ""

#: inc/gutenberg_color_palette.php:23
msgid "Black"
msgstr ""

#: inc/gutenberg_color_palette.php:28
msgid "Very Dark Grey"
msgstr ""

#: inc/gutenberg_color_palette.php:33
msgid "Dark Grey"
msgstr ""

#: inc/gutenberg_color_palette.php:38
msgid "Medium Grey"
msgstr ""

#: inc/gutenberg_color_palette.php:43
msgid "Light Grey"
msgstr ""

#: inc/gutenberg_color_palette.php:48
msgid "Very Light Grey"
msgstr ""

#: inc/gutenberg_color_palette.php:53
msgid "Light/Dark Theme Highlight"
msgstr ""

#: inc/gutenberg_color_palette.php:67
msgid "Primary to Secondary"
msgstr ""

#: inc/gutenberg_color_palette.php:72
msgid "Secondary to Tertiary"
msgstr ""

#: inc/gutenberg_color_palette.php:77
msgid "Primary to Tertiary"
msgstr ""

#: searchform.php:17
msgid "Search for:"
msgstr ""

#: searchform.php:19
msgid "Search and press enter"
msgstr ""

#: sidebar-search.php:14
msgid "Search Sidebar"
msgstr ""

#: sidebar-slide.php:23
msgid "Blog Sidebar"
msgstr ""

#: sidebar.php:23
msgid "Sidebar"
msgstr ""

#: template-parts/page/content.php:159
msgid "Pages:"
msgstr ""

#: template-parts/post/content-none.php:27
msgid ""
"Sorry, nothing matched your search terms. Please try again with some "
"different keywords"
msgstr ""

#: template-parts/post/content.php:268
msgid "Next page"
msgstr ""

#: template-parts/post/content.php:269
msgid "Previous page"
msgstr ""

#: template-parts/post/hentry-footer.php:50
msgid "read more"
msgstr ""

#: template-parts/post/read-more.php:15
msgid "Continue Reading"
msgstr ""

#: template-parts/post/single-authorbio.php:32
msgid "Written by"
msgstr ""

#: template-parts/post/single-post-navigation.php:48
msgid "More Reading"
msgstr ""

#: template-parts/post/single-post-navigation.php:69
#: template-parts/post/single-post-navigation.php:111
msgid "Previous Post"
msgstr ""

#: template-parts/post/single-post-navigation.php:170
#: template-parts/post/single-post-navigation.php:209
msgid "Next Post"
msgstr ""
