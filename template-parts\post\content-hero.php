<?php
/**
 * Template part for displaying posts
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

?>

<?php 

$tfm_vars = tfm_template_vars( 'content', false);
$hero_style = ( 'cover' === $tfm_vars['content']['post_style'] ? 'cover' : 'default' );
//echo $tfm_vars['content']['full_width'];
// $full_width = $tfm_vars['content']['full_width'] ? 'true' : 'false';
$margins = ( 'true' === $full_width ? 'false' : 'true' );
$has_background = '' !== get_theme_mod( 'tfm_single_hero_background', '') ? ' has-background' : '';
$has_thumbnail = '' !== get_the_post_thumbnail() && ! $tfm_vars['content']['disabled_thumbnail'] ? 'true' : 'false';

?>

<div <?php post_class( 'single-hero' ) ?> data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">

	<?php if ( $tfm_vars['content']['post_style'] === 'cover' ): ?>

	<div class="post-inner">

	<div class="cover-wrapper" data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">

	<?php endif; ?>

	<?php

	// ========================================================
	// Post thumbnail & featured video/audio
	// ========================================================

	if ( '' !== get_the_post_thumbnail() && ! $tfm_vars['content']['disabled_thumbnail'] && ! tfm_featured_video( true )
		&& ! tfm_featured_audio( true) ) : ?>

		<div class="thumbnail-wrapper" data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">

			<figure class="post-thumbnail<?php echo esc_attr( $tfm_vars['content']['figcaption'] ); ?>">

				<?php tfm_get_post_thumbnail(); ?>

			</figure>

			<?php if ( $tfm_vars['content']['figcaption'] ) : // Figcaption ?>

					<figcaption class="featured-media-caption"><?php echo esc_html( get_the_post_thumbnail_caption() ); ?></figcaption>

				<?php endif; ?>

		</div>
		
	<?php endif; ?>

	<?php

	// featured video/audio

	if ( has_post_format('video') || has_post_format( 'audio')) :

	 	echo tfm_featured_video();
		tfm_featured_audio();

	endif; ?>

	<?php if ( $tfm_vars['content']['post_style'] !== 'default' ): ?>

	<header class="entry-header single-entry-header">
		<?php

		// ========================================================
		// Entry header
		// ========================================================

		if ( 'post' === get_post_type() ) {

		$is_multi_line = tfm_toggle_entry_meta( 'author' ) && tfm_toggle_entry_meta( 'date' ) && tfm_toggle_entry_meta( 'author_avatar' ) ? true : false;
		$date = tfm_toggle_entry_meta( 'date' ) && tfm_toggle_entry_meta( 'author' ) && tfm_toggle_entry_meta( 'author_nickname' ) && '' !== get_the_author_meta( 'nickname' ) ? '' : 'date';
			$date_after_meta = tfm_toggle_entry_meta( 'date' ) && tfm_toggle_entry_meta( 'author' ) && tfm_toggle_entry_meta( 'author_nickname' ) && '' !== get_the_author_meta( 'nickname' ) ? 'date' : '';

		tfm_get_entry_meta( $meta_data = array('author_avatar', 'author', $date), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = 'author-meta', $meta_class = '', $cats_wrapper = false, $multi_line = $is_multi_line );

		}

		tfm_before_entry_title(); // hook

		the_title( '<h1 class="entry-title single-entry-title">', '</h1>' );

		tfm_after_entry_title(); // hook

		if ( 'post' === get_post_type() ) {

			tfm_get_entry_meta( $meta_data = array($date_after_meta, 'comment_count', 'read_time', 'post_views'), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = '', $meta_class = '' );

		}

		?>

		<?php tfm_get_excerpt( ); ?>

		<?php //tfm_entry_header_close();  ?>

		<?php tfm_single_hero_entry_header_close();  ?>

	</header>

	<?php tfm_single_hero_cover_wrapper_close(); ?>

<?php endif; // endif default-alt/cover ?>
<?php if ( $tfm_vars['content']['post_style'] === 'cover' ): ?>
	</div>
	</div>
<?php endif; ?>


</div><!-- .article -->