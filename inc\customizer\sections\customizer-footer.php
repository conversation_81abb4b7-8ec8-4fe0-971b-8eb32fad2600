<?php


/**
 * Footer Settings
 *
 * @package WordPress
 * @subpackage jinko
 */

function tfm_customize_register_footer( $wp_customize ) {

	$customizer_settings = tfm_general_settings();

	$wp_customize->add_section( 'tfm_footer_settings', array(
		'title'    => esc_html__( 'Footer Settings', 'jinko' ),
		'priority' => 130,
		'panel' => 'tfm_theme_settings',
	) );

	// Footer text
	$wp_customize->add_setting( 'tfm_footer_text', array(
		'default'           => get_bloginfo('description'),
		'sanitize_callback' => 'tfm_sanitize_html',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_footer_text', array(
		'label'       => esc_html__( 'Footer Text', 'jinko' ),
		'description' => esc_html__( 'Basic HTML is supported', 'jinko'),
		'section'     => 'tfm_footer_settings',
		'type'        => 'textarea',
	) );

}

add_action( 'customize_register', 'tfm_customize_register_footer' );