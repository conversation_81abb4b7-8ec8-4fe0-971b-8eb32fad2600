<?php


/**
 * Light color settings
 *
 * @package WordPress
 * @subpackage jinko
 */

function tfm_customize_register_light_theme_colors( $wp_customize ) {

	$customizer_settings = tfm_general_settings();

	$scheme = '';

	$wp_customize->add_section( 'tfm_theme_colors' . $scheme, array(
		'title'    => esc_html__( 'Light Theme', 'jinko' ),
		'priority' => 140,
		'panel' => 'tfm_color_settings',
	) );

	// ========================================================
	// Include scheme file
	// ========================================================

	require( get_template_directory() . '/inc/customizer/sections/colors/scheme.php' );

}

add_action( 'customize_register', 'tfm_customize_register_light_theme_colors' );