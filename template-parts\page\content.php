<?php
/**
 * Template part for displaying posts
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

?>

<?php  $tfm_vars = tfm_template_vars( 'content', false ); ?>

<?php $show_frontpage_title = is_front_page() ? get_theme_mod( 'tfm_front_page_entry_title', true ) : true;  ?>

<article id="page-<?php the_ID(); ?>" <?php post_class( ); ?>>

	<div class="post-inner">

	<?php 

	tfm_post_inner_open();

	// ========================================================
	// Open single cover wrapper
	// ========================================================

	/**
	 * Wraps the featured image and entry header
	 */

	if ( $tfm_vars['content']['cover_wrapper'] ) : ?>

		<div class="cover-wrapper single-cover-wrapper" data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">


	<?php endif;

	// ========================================================
	// Post thumbnail
	// ========================================================

	 if ( $tfm_vars['content']['post_thumbnail'] ) : ?>

		<div class="thumbnail-wrapper" data-fullwidth="<?php echo esc_attr($tfm_vars['content']['full_width']); ?>">

			<?php get_template_part( 'template-parts/post/sticky-formats' ); ?>

			<?php tfm_inside_thumbnail_wrapper(); ?>

			<figure class="post-thumbnail<?php echo esc_attr( $tfm_vars['content']['figcaption'] ); ?>">

			<?php tfm_get_post_thumbnail( $size = '', $aspect_ratio = $tfm_vars['content']['thumbnail_aspect_ratio'], $count = 0 ); ?>
				
			</figure>

			<?php if ( $tfm_vars['content']['figcaption'] ) : // Figcaption ?>

				<figcaption class="featured-media-caption"><?php echo esc_html( get_the_post_thumbnail_caption() ); ?></figcaption>

			<?php endif; ?>

		</div>
		
	<?php endif; 

	// ========================================================
	// Open cover & list layout content wrapper (archive)
	// ========================================================

	if ( $tfm_vars['content']['entry_wrapper'] ) : ?>

	<div class="entry-wrapper">

	<?php

	endif;

	// ========================================================
	// Single entry header, meta and entry title
	// ========================================================

	if ( $tfm_vars['content']['single_entry_header'] ) : ?>

		<header class="entry-header single-entry-header">

	<?php endif;

	// Only show entry meta and title if archive, single or hero-default layout
	if ( $tfm_vars['content']['single_entry_header'] ) :

		// ========================================================
		// entry title
		// ========================================================

		if ( $show_frontpage_title ) :

			the_title( '<h1 class="entry-title single-entry-title">', '</h1>' );

		endif;

		// ========================================================
		// Close single entry header
		// ========================================================

		endif; // endif hero entrey header

		if ( $tfm_vars['content']['single_entry_header'] ) : ?>

			</header>

		<?php endif;

		// ========================================================
		// Close single cover wrapper
		// ========================================================

		if ( $tfm_vars['content']['cover_wrapper']) : ?>

			</div>

		<?php endif;

			get_template_part( 'template-parts/page/hentry-footer' ); 

	// ========================================================
	// Close cover & list layout content wrapper (archive)
	// ========================================================

	if ( $tfm_vars['content']['entry_wrapper'] ) : ?>

	</div>

	<?php endif;

	// ========================================================
	// Single content and hentry footer
	// ========================================================

	?>

			<div class="single-content-wrapper">

				<?php tfm_before_single_content(); ?>

				<div class="entry-content">

					<?php

						the_content( );

						tfm_after_the_content();


						wp_link_pages( array(
							'before'      => '<div class="nav-links">' . esc_html__( 'Pages:', 'jinko' ),
							'after'       => '</div>',
							'link_before' => '<span class="page-number">',
							'link_after'  => '</span>',
						) );

				    ?>

				</div><!-- .entry-content -->

			</div><!-- .single-content-wrapper -->

		<?php

			// Get post tags and share template
			get_template_part( 'template-parts/page/hentry-footer' ); 


		tfm_post_inner_close();

		?>

	</div><!-- .post-inner -->

</article>
