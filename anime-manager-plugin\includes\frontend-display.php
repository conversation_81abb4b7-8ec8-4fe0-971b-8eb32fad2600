<?php
/**
 * قالب عرض تفاصيل الأنمي في الواجهة الأمامية
 */

// تحميل ووردبريس core
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('Error: Unable to load WordPress core files');
}

// تحميل ملفات ووردبريس الأساسية
if (defined('ABSPATH')) {
    if (file_exists(ABSPATH . 'wp-includes/pluggable.php')) {
        require_once(ABSPATH . 'wp-includes/pluggable.php');
    } else {
        die('Error: Unable to load WordPress pluggable functions');
    }
    
    if (file_exists(ABSPATH . 'wp-admin/includes/plugin.php')) {
        require_once(ABSPATH . 'wp-admin/includes/plugin.php');
    } else {
        die('Error: Unable to load WordPress plugin functions');
    }
} else {
    die('Error: ABSPATH not defined');
}

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// التحقق من وجود بيانات لعرضها
$has_data = !empty($anime_type) || !empty($anime_duration) || !empty($anime_status) || 
            !empty($anime_episodes) || !empty($anime_season) || !empty($anime_year) || 
            (!empty($anime_links) && is_array($anime_links));

if (!$has_data) {
    return;
}
?>

<div class="anime-modern-container">
    <div class="anime-details-card modern-card">
        <div class="anime-details-header modern-header">
            <h3 class="anime-details-title modern-title">
                <span class="anime-details-icon modern-icon">📺</span>
                <?php _e('تفاصيل الأنمي', 'anime-manager'); ?>
            </h3>
        </div>
        
        <div class="anime-details-content modern-content">
            <div class="anime-details-grid modern-grid">
            
            <!-- صورة الغلاف -->
            <?php if (has_post_thumbnail()): ?>
                <div class="anime-cover-section modern-cover">
                    <div class="anime-cover-wrapper modern-cover-wrapper">
                        <?php the_post_thumbnail('medium', array(
                            'class' => 'anime-cover-image modern-cover-img',
                            'alt' => get_the_title()
                        )); ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- المعلومات الأساسية -->
            <div class="anime-info-section">
                <div class="anime-info-grid">
                    
                    <?php if (!empty($anime_type)): ?>
                        <div class="anime-info-item modern-info-item">
                            <span class="anime-info-label modern-info-label">
                                <span class="anime-info-icon modern-info-icon">🎬</span>
                                <?php _e('النوع:', 'anime-manager'); ?>
                            </span>
                            <span class="anime-info-value modern-info-value anime-type-<?php echo esc_attr(strtolower($anime_type)); ?>">
                                <?php echo esc_html($anime_type); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($anime_duration)): ?>
                        <div class="anime-info-item">
                            <span class="anime-info-label">
                                <span class="anime-info-icon">⏱️</span>
                                <?php _e('مدة الحلقة:', 'anime-manager'); ?>
                            </span>
                            <span class="anime-info-value">
                                <?php echo esc_html($anime_duration); ?> <?php _e('دقيقة', 'anime-manager'); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($anime_status)): ?>
                        <div class="anime-info-item">
                            <span class="anime-info-label">
                                <span class="anime-info-icon">📊</span>
                                <?php _e('الحالة:', 'anime-manager'); ?>
                            </span>
                            <span class="anime-info-value anime-status-<?php echo esc_attr(str_replace(' ', '-', strtolower($anime_status))); ?>">
                                <?php echo esc_html($anime_status); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($anime_episodes)): ?>
                        <div class="anime-info-item">
                            <span class="anime-info-label">
                                <span class="anime-info-icon">📺</span>
                                <?php _e('عدد الحلقات:', 'anime-manager'); ?>
                            </span>
                            <span class="anime-info-value">
                                <?php echo esc_html($anime_episodes); ?> <?php _e('حلقة', 'anime-manager'); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($anime_season) || !empty($anime_year)): ?>
                        <div class="anime-info-item">
                            <span class="anime-info-label">
                                <span class="anime-info-icon">🗓️</span>
                                <?php _e('الموسم:', 'anime-manager'); ?>
                            </span>
                            <span class="anime-info-value">
                                <?php 
                                if (!empty($anime_season) && !empty($anime_year)) {
                                    echo esc_html($anime_season . ' ' . $anime_year);
                                } elseif (!empty($anime_season)) {
                                    echo esc_html($anime_season);
                                } elseif (!empty($anime_year)) {
                                    echo esc_html($anime_year);
                                }
                                ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                </div>
            </div>
        </div>
        
        <!-- الروابط الخارجية -->
        <?php if (!empty($anime_links) && is_array($anime_links)): ?>
            <div class="anime-links-section modern-links-section">
                <h4 class="anime-links-title modern-links-title">
                    <span class="anime-info-icon modern-links-icon">🔗</span>
                    <?php _e('روابط مفيدة', 'anime-manager'); ?>
                </h4>
                <div class="anime-links-grid modern-links-grid">
                    <?php foreach ($anime_links as $link): ?>
                        <?php if (!empty($link['url'])): ?>
                            <a href="<?php echo esc_url($link['url']); ?>" 
                               target="_blank" 
                               rel="noopener noreferrer" 
                               class="anime-external-link modern-external-link">
                                <span class="anime-link-icon modern-link-icon">🌐</span>
                                <span class="anime-link-text modern-link-text">
                                    <?php echo esc_html(!empty($link['title']) ? $link['title'] : $link['url']); ?>
                                </span>
                                <span class="anime-link-arrow modern-link-arrow">↗</span>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
