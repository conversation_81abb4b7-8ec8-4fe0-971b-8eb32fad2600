# دليل القالب المخصص لصفحات الأنمي

## 🎨 التصميم الجديد

تم إنشاء قالب مخصص جديد لصفحات الأنمي يوفر:

### ✨ المميزات الجديدة:
- **صورة بحجم كامل**: تملأ الشاشة بالكامل
- **عنوان فوق الصورة**: مع تأثير تدرج أنيق
- **تفاصيل منظمة**: في بطاقات جميلة أسفل الصورة
- **تصميم نظيف**: بدون عناصر إضافية غير مرغوب فيها

## 📁 الملفات المضافة:

### 1. `single-anime.php`
- قالب مخصص لصفحات الأنمي المفردة
- يحل محل القالب الافتراضي `single.php`
- يعرض الصورة بحجم كامل مع العنوان فوقها

### 2. `assets/anime-single-style.css`
- تنسيقات مخصصة للقالب الجديد
- يخفي العناصر غير المرغوب فيها
- تصميم متجاوب لجميع الأجهزة

## 🚀 كيفية التفعيل:

### الطريقة الأولى: النسخ التلقائي
1. الملفات موجودة بالفعل في مجلد الإضافة
2. انسخ `single-anime.php` إلى مجلد القالب الرئيسي
3. ستعمل تلقائياً مع جميع صفحات الأنمي

### الطريقة الثانية: التفعيل اليدوي
```bash
# انسخ القالب إلى مجلد القالب
cp anime-manager-plugin/single-anime.php ../single-anime.php
```

## 🎯 كيف يعمل:

### 1. اكتشاف تلقائي
- ووردبريس يبحث عن `single-anime.php` تلقائياً
- إذا وُجد، يستخدمه بدلاً من `single.php`
- إذا لم يوجد، يستخدم العرض الافتراضي

### 2. إخفاء العناصر
```css
/* يخفي العناصر غير المرغوب فيها */
.single-anime .thumbnail-wrapper,
.single-anime .entry-header.single-entry-header {
    display: none !important;
}
```

### 3. الصورة بحجم كامل
```css
.anime-hero-image {
    width: 100vw;
    height: 100vh;
    margin-left: calc(-50vw + 50%);
}
```

## 🎨 التخصيص:

### تغيير ارتفاع الصورة:
```css
.anime-hero-image {
    height: 80vh; /* بدلاً من 100vh */
}
```

### تغيير موضع العنوان:
```css
.anime-title-overlay {
    top: 50%; /* في المنتصف */
    bottom: auto;
    transform: translateY(-50%);
}
```

### تغيير ألوان التدرج:
```css
.anime-title-overlay {
    background: linear-gradient(transparent, rgba(108,91,123,0.9));
}
```

## 📱 التجاوب:

### الشاشات الكبيرة (1024px+):
- صورة بحجم كامل 100vh
- تفاصيل في شبكة متعددة الأعمدة

### الأجهزة اللوحية (768px-1024px):
- صورة بارتفاع 70vh
- تفاصيل في عمودين

### الهواتف (أقل من 768px):
- صورة بارتفاع 60vh
- تفاصيل في عمود واحد
- عناصر مكدسة عمودياً

## 🔧 استكشاف الأخطاء:

### المشكلة: لا يظهر القالب الجديد
**الحل:**
1. تأكد من وجود `single-anime.php` في مجلد القالب
2. امسح ذاكرة التخزين المؤقت
3. تحقق من أن نوع المنشور هو 'anime'

### المشكلة: الصورة لا تملأ الشاشة
**الحل:**
1. تأكد من تحميل `anime-single-style.css`
2. تحقق من عدم تعارض CSS أخرى
3. افحص أدوات المطور في المتصفح

### المشكلة: العناصر القديمة ما زالت تظهر
**الحل:**
1. امسح ذاكرة التخزين المؤقت
2. تحقق من ترتيب تحميل ملفات CSS
3. أضف `!important` إذا لزم الأمر

## 🎯 نصائح للتحسين:

### 1. تحسين الصور:
- استخدم صور عالية الجودة (1920x1080 أو أكبر)
- احفظ الصور بصيغة WebP للأداء الأفضل
- استخدم أدوات ضغط الصور

### 2. تحسين الأداء:
```php
// إضافة lazy loading للصور
the_post_thumbnail('full', array(
    'loading' => 'lazy',
    'class' => 'anime-cover-full'
));
```

### 3. إضافة تأثيرات:
```css
.anime-cover-full {
    filter: brightness(0.8) contrast(1.1);
    transition: filter 0.3s ease;
}

.anime-hero-image:hover .anime-cover-full {
    filter: brightness(1) contrast(1);
}
```

## 🔄 التحديثات المستقبلية:

- [ ] إضافة معرض صور للأنمي
- [ ] تأثيرات parallax للصورة الرئيسية
- [ ] نظام تقييم بالنجوم
- [ ] مشاركة على وسائل التواصل
- [ ] روابط الأنمي ذات الصلة

## 📞 الدعم:

إذا واجهت أي مشاكل مع القالب الجديد:
1. راجع هذا الدليل أولاً
2. تحقق من سجل أخطاء ووردبريس
3. جرب إلغاء تفعيل الإضافات الأخرى مؤقتاً
4. تواصل معنا للدعم الفني

---

**استمتع بالتصميم الجديد الرائع! 🎌**
