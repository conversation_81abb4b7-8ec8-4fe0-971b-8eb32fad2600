<?php

/**
 * Theme Settings
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

// ========================================================
// Theme Settings
// ========================================================

/**
 * set up default customizer setings
 * and light dark colour settings
 */

function tfm_general_settings( ) {

	return array(
		// general & theme specific settings
		'site_width' => 1400,
		'sidebar_width' => 320,
		'content_max_width' => 800,
		'no_sidebar_blog_max_width' => 920,
		'default_border_radius' => 8,
		'thumbnail_border_radius' => 10,
		'button_border_radius' => 8,
		'input_border_radius' => 8,
		'primary_menu_font_size' => 14,
		'single_entry_font_size' => 19,

		////////////////////////////////////////////////
		// Light theme
		////////////////////////////////////////////////

		'default_post_background' => '#ffffff',
		'primary_theme_color' => '#355c7d',
		'secondary_theme_color' => '#f67280',
		'tertiary_theme_color' => '#f8b195',
		// body
		'tfm_body_background_color' => '#f4f5f9',
		'tfm_body_background_gradient' => '',
		'tfm_body_font_color' => '#131315',
		'tfm_link_color' => '#355c7d',
		'tfm_link_hover_color' => '#f67280',
		// buttons
		'tfm_button_background' => '#c06c84',
		'tfm_button_color' => '#ffffff',
		'tfm_button_hover_background' => '#f67280',
		'tfm_button_hover_color' => '#ffffff',
		// header colours
		'tfm_header_background' => '',
		'tfm_header_color' => '#131315',
		'tfm_header_logo_color' => '#000000',
		'tfm_header_elements_background' => '#ffffff',
		'tfm_header_elements_color' => '#44464b',
		// footer colours
		'tfm_footer_background' => '#fafafa',
		'tfm_footer_font_color' => '#131315',
		'tfm_footer_link_color' => '#131315',
		// category header
		'tfm_archive_header_background' => '',
		'tfm_archive_header_color' => '#131315',
		'tfm_archive_header_meta_color' => '#94979e',
		// section header
		'tfm_section_header_color' => '#131315',
		'tfm_section_header_meta_color' => '#94979e',
		// entry
		'tfm_entry_title_link_color' => '#131315',
		'tfm_entry_color' => '#131315', // single
		'tfm_entry_link_color' => '#355c7d', // single
		'tfm_entry_link_hover_color' => '#f67280', // single
		'tfm_entry_meta_color' => '#94979e',
		'tfm_entry_meta_link_color' => '#131315',
		'tfm_post_format_icon_color' => '#ffffff',
		'tfm_post_format_icon_background' => '#f67280',
		'tfm_post_format_audio_icon_background' => '#c06c84',
		'tfm_post_format_video_icon_background' => '#355c7d',
		'tfm_post_format_gallery_icon_background' => '#f67280',
		'tfm_continue_reading_button_background' => '#f2f2f3',
		'tfm_continue_reading_button_color' => '#44464b',
		'tfm_continue_reading_button_hover_background' => '#f67280',
		'tfm_continue_reading_button_hover_color' => '#ffffff',
		'tfm_cover_format_primary_color' => '#ffffff',
		// widgets
		'tfm_widget_background' => '',
		'tfm_widget_color' => '#94979e',
		'tfm_widget_link_color' => '#131315',
		'tfm_widget_title_color' => '#131315',
		// tags
		'tfm_post_tag_background' => '#ffffff',
		'tfm_post_tag_color' => '#131315',
		'category_slug_background' => '',
		'category_slug_color' => '#131315',
		// misc
		'tfm_cta_background' => '',
		'tfm_cta_color' => '#355c7d',
		'tfm_cta_background_hover' => '',
		'tfm_cta_color_hover' => '#355c7d',

		////////////////////////////////////////////////
		// Dark theme
		////////////////////////////////////////////////

		'default_post_background_dark' => '#321d2f',
		'primary_theme_color_dark' => '#da4453',
		'secondary_theme_color_dark' => '#89216b',
		'tertiary_theme_color_dark' => '#f8b195',
		// body
		'tfm_body_background_color_dark' => '#31253F',
		'tfm_body_background_gradient_dark' => '',
		'tfm_body_font_color_dark' => '#cfd0d2',
		'tfm_link_color_dark' => '#ffffff',
		'tfm_link_hover_color_dark' => '#cfd0d2',
		// buttons
		'tfm_button_background_dark' => '#4c5f7a',
		'tfm_button_color_dark' => '#ffffff',
		'tfm_button_hover_background_dark' => '#393E6F',
		'tfm_button_hover_color_dark' => '#ffffff',
		// header colours
		'tfm_header_background_dark' => '',
		'tfm_header_color_dark' => '#ffffff',
		'tfm_header_logo_color_dark' => '#ffffff',
		'tfm_header_elements_background_dark' => '#321d2f',
		'tfm_header_elements_color_dark' => '#ffffff',
		// footer colours
		'tfm_footer_background_dark' => '#31253f',
		'tfm_footer_font_color_dark' => '#ffffff',
		'tfm_footer_link_color_dark' => '#ffffff',
		// category header
		'tfm_archive_header_background_dark' => '',
		'tfm_archive_header_color_dark' => '#ffffff',
		'tfm_archive_header_meta_color_dark' => '#cfd0d2',
		// section header
		'tfm_section_header_color_dark' => '#ffffff',
		'tfm_section_header_meta_color_dark' => '#94979e',
		// entry
		'tfm_entry_title_link_color_dark' => '#ffffff',
		'tfm_entry_color_dark' => '#ffffff', // single
		'tfm_entry_link_color_dark' => '#da4453', // single
		'tfm_entry_link_hover_color_dark' => '#f8b195', // single
		'tfm_entry_meta_color_dark' => '#cfd0d2', // theme light grey
		'tfm_entry_meta_link_color_dark' => '#ffffff',
		'tfm_post_format_icon_color_dark' => '#ffffff',
		'tfm_post_format_icon_background_dark' => '#da4453',
		'tfm_post_format_audio_icon_background_dark' => '#89216b',
		'tfm_post_format_video_icon_background_dark' => '#ffb14f',
		'tfm_post_format_gallery_icon_background_dark' => '#da4453',
		'tfm_continue_reading_button_background_dark' => '#4c5f7a',
		'tfm_continue_reading_button_color_dark' => '#ffffff',
		'tfm_continue_reading_button_hover_background_dark' => '#393E6F',
		'tfm_continue_reading_button_hover_color_dark' => '#ffffff',
		'tfm_cover_format_primary_color_dark' => '#ffffff',
		// widgets
		'tfm_widget_background_dark' => '',
		'tfm_widget_color_dark' => '#94979e',
		'tfm_widget_link_color_dark' => '#ffffff',
		'tfm_widget_title_color_dark' => '#ffffff',
		// tags
		'tfm_post_tag_background_dark' => '',
		'tfm_post_tag_color_dark' => '',
		'category_slug_background_dark' => '',
		'category_slug_color_dark' => '#ffffff',
		// misc
		'tfm_cta_background_dark' => '',
		'tfm_cta_color_dark' => '',
		'tfm_cta_background_hover_dark' => '',
		'tfm_cta_color_hover_dark' => '',

	);

}

?>