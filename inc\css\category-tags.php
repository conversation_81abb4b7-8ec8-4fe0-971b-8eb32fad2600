<?php

// ========================================================
// Category & Tag Slug colors
// ========================================================

$categories = apply_filters( 'tfm_set_category_colors', true ) ? array_slice( get_categories('type=post'), 0, apply_filters( 'tfm_set_category_colors_max_count', 50 ) ) : false;
$tags = apply_filters( 'tfm_set_tag_colors', true ) ? array_slice( get_tags(), 0, apply_filters( 'tfm_set_tag_colors_max_count', 50 ) ) : false;
$shop_categories = class_exists('WooCommerce') ? get_categories( array( 'taxonomy' => 'product_cat') ) : false;

if ( $categories || $tags || $shop_categories) :

	// Categories
	if ( $categories ) {

		// Custom category tags
		foreach ( $categories as $key => $value) {

		$slug_color = '' !== get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) ? 'color:' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ';' : '';
		$slug_background = '' !== get_theme_mod( 'category_slug_background_' . $value->slug . '', '' ) ? 'background:' . get_theme_mod( 'category_slug_background_' . $value->slug . '', '' ) . ' !important; border: 0 !important;' : '';

			$custom_slug_color[] =  ( $slug_color || $slug_background ? '.entry-meta a.cat-link-' . $value->term_id . ', [class*="cat-slug"] a.cat-link-' . $value->term_id . ' { ' . $slug_color . $slug_background . '} .widget a.tag-link-' . $value->term_id . ', .sub-categories a.cat-link-' . $value->term_id . ' { ' . $slug_color . $slug_background . ' }' : '' );

			$custom_slug_color[] =  $slug_color ? '.entry-meta a.cat-link-' . $value->term_id . '::before, [class*="cat-slug"] a.cat-link-' . $value->term_id . '::before { background: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ' ; }' : '' ;
			$custom_slug_color[] =  $slug_color ? '.category-' . $value->term_id . ' .archive-header .post-count{ color: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ' !important; border-color: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ' !important  }' : '' ;
			$custom_slug_color[] =  $slug_color ? '.widget_categories .cat-item-' . $value->term_id . ' .tfm-count { color: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . '; outline-color: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ';}' : '' ;

			$custom_slug_color[] =  $slug_color ? '.primary-menu .tfm-categories ul a.cat-link-' . $value->term_id . ' .menu-label { color: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ' ; }' : '' ;
			$custom_slug_color[] =  $slug_color ? '.primary-menu > .menu-item-object-category a.cat-link-' . $value->term_id . ':hover, .primary-menu > .menu-item-object-category a.cat-link-' . $value->term_id . ':focus, .primary-menu > .menu-item-object-category.current-menu-item a.cat-link-' . $value->term_id . '  { color: ' . get_theme_mod( 'category_slug_color_' . $value->slug . '', '' ) . ' ; }' : '' ;
			
		}

	}

	// Tags
	if ( $tags ) {
		
		foreach ($tags as $key => $value) {

			$custom_slug_color[] =  ( '' !== get_theme_mod( 'tag_slug_color_' . $value->slug . '', '' ) ? '.single-post-tags a.tag-link-' . $value->term_id . ', .widget a.tag-link-' . $value->term_id . ' { background:' . get_theme_mod( 'tag_slug_color_' . $value->slug . '', '' ) . '; color: #ffffff }' : '' );
			
		}

	}

	// Shop Categories
	if ( $shop_categories) {

		foreach ($shop_categories as $key => $value) {

			$custom_slug_color[] =  ( '' !== get_theme_mod( 'woo_category_slug_color_' . $value->slug . '', '' ) ? '.entry-meta a.cat-link-' . $value->term_id . ' { color:' . get_theme_mod( 'woo_category_slug_color_' . $value->slug . '', '' ) . '; }' : '' );
			
		}

	}

	$slug_colors = array_filter($custom_slug_color);

	if ( count($slug_colors) !== 0 ) :

		echo '<style type="text/css" id="jinko-custom-slug-css">';
		foreach ($slug_colors as $css ) {
			echo wp_strip_all_tags( $css ) . "\n";
		}
		echo  '</style>' . "\n";

	endif;

endif;