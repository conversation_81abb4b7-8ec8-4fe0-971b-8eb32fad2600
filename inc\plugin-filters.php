<?php

// ========================================================
// Add post view count to widget count wrapper
// ========================================================
function tfm_posts_widget_post_count_post_view( ) {

	if ( function_exists('tfm_post_views') ) {
		tfm_post_views();
	}

}
add_action( 'tfm_posts_widget_tfm_count_wrapper', 'tfm_posts_widget_post_count_post_view' );


// ========================================================
// Move blog header to after featured posts if before loop
// ========================================================
if ( function_exists( 'tfm_featured_posts' ) && tfm_featured_posts_active( ) && get_theme_mod( 'tfm_featured_posts_location', 'tfm_after_header') === 'tfm_before_loop' ) {
	remove_action( 'tfm_after_wrap_inner', 'tfm_blog_header', 30 );
	add_action( 'tfm_before_loop', 'tfm_blog_header', 30 );
}

// ========================================================
// Remove PVC display
// ========================================================

function jinko_remove_pvc_archive_views() {
    return false;
}
add_filter( 'pvc_shortcode_filter_hook', 'jinko_remove_pvc_archive_views' );