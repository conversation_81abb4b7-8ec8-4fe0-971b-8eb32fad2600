/**
 * تنسيقات لوحة الإدارة - إضافة مدير الأنمي
 * متوافق مع تصميم ووردبريس الحديث
 */

/* الحاوي الرئيسي */
.anime-metabox-container {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
}

/* الشبكة الرئيسية */
.anime-metabox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* مجموعة الحقول */
.anime-field-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* تسميات الحقول */
.anime-field-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
    color: #1d2327;
    margin-bottom: 5px;
}

.anime-field-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* الحقول النصية والرقمية */
.anime-select-field,
.anime-number-field {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.anime-select-field:focus,
.anime-number-field:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* قسم الروابط الخارجية */
.anime-links-section {
    grid-column: 1 / -1;
    border-top: 1px solid #dcdcde;
    padding-top: 20px;
    margin-top: 10px;
}

.anime-links-header {
    font-size: 16px;
    margin-bottom: 10px;
}

.anime-field-description {
    color: #646970;
    font-size: 13px;
    margin: 0 0 15px 0;
    line-height: 1.4;
}

/* حاوي الروابط */
.anime-links-container {
    margin-bottom: 15px;
}

.anime-link-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 15px;
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.anime-link-item:hover {
    background: #f0f0f1;
}

.anime-link-inputs {
    display: flex;
    gap: 10px;
    flex: 1;
}

.anime-link-title,
.anime-link-url {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
}

.anime-link-title:focus,
.anime-link-url:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* زر حذف الرابط */
.anime-remove-link {
    background: #d63638;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.anime-remove-link:hover {
    background: #b32d2e;
}

.anime-remove-link .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* زر إضافة رابط */
.anime-add-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    background: #2271b1;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.anime-add-link-btn:hover {
    background: #135e96;
    color: #fff;
}

.anime-add-link-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* قسم النصائح */
.anime-tips-section {
    grid-column: 1 / -1;
    background: #f0f6fc;
    border: 1px solid #c3dcf3;
    border-radius: 6px;
    padding: 20px;
    margin-top: 20px;
}

.anime-tips-section h4 {
    margin: 0 0 15px 0;
    color: #0073aa;
    font-size: 15px;
}

.anime-tips-list {
    margin: 0;
    padding-right: 20px;
    color: #2c3338;
}

.anime-tips-list li {
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 13px;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 782px) {
    .anime-metabox-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .anime-link-inputs {
        flex-direction: column;
        gap: 8px;
    }
    
    .anime-link-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .anime-remove-link {
        align-self: flex-end;
        width: fit-content;
    }
}

/* تحسينات إضافية للتصميم */
.anime-metabox-container input[type="number"]::-webkit-outer-spin-button,
.anime-metabox-container input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.anime-metabox-container input[type="number"] {
    -moz-appearance: textfield;
}

/* حالات التحقق من صحة البيانات */
.anime-field-group.has-error .anime-select-field,
.anime-field-group.has-error .anime-number-field,
.anime-field-group.has-error .anime-link-title,
.anime-field-group.has-error .anime-link-url {
    border-color: #d63638;
    box-shadow: 0 0 0 1px #d63638;
}

/* رسائل التحقق */
.anime-field-error {
    color: #d63638;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* تحسين مظهر القوائم المنسدلة */
.anime-select-field {
    background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>");
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 12px;
    padding-left: 35px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* للغة العربية - تعديل اتجاه السهم */
[dir="rtl"] .anime-select-field {
    background-position: right 12px center;
    padding-left: 12px;
    padding-right: 35px;
}
