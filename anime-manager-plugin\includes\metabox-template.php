<?php
/**
 * قالب صندوق البيانات الإضافية للأنمي
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="anime-metabox-container">
    <div class="anime-metabox-grid">
        
        <!-- النوع -->
        <div class="anime-field-group">
            <label for="anime_type" class="anime-field-label">
                <span class="anime-field-icon">🎬</span>
                <?php _e('النوع', 'anime-manager'); ?>
            </label>
            <select id="anime_type" name="_anime_type" class="anime-select-field">
                <option value=""><?php _e('اختر النوع', 'anime-manager'); ?></option>
                <option value="تلفازي" <?php selected($anime_type, 'تلفازي'); ?>><?php _e('تلفازي', 'anime-manager'); ?></option>
                <option value="بلوراي" <?php selected($anime_type, 'بلوراي'); ?>><?php _e('بلوراي', 'anime-manager'); ?></option>
                <option value="ويب" <?php selected($anime_type, 'ويب'); ?>><?php _e('ويب', 'anime-manager'); ?></option>
            </select>
        </div>

        <!-- مدة الحلقة -->
        <div class="anime-field-group">
            <label for="anime_duration" class="anime-field-label">
                <span class="anime-field-icon">⏱️</span>
                <?php _e('مدة الحلقة (بالدقائق)', 'anime-manager'); ?>
            </label>
            <input type="number" 
                   id="anime_duration" 
                   name="_anime_duration" 
                   value="<?php echo esc_attr($anime_duration); ?>" 
                   class="anime-number-field"
                   min="1" 
                   max="300"
                   placeholder="<?php _e('مثال: 24', 'anime-manager'); ?>">
        </div>

        <!-- الحالة -->
        <div class="anime-field-group">
            <label for="anime_status" class="anime-field-label">
                <span class="anime-field-icon">📊</span>
                <?php _e('الحالة', 'anime-manager'); ?>
            </label>
            <select id="anime_status" name="_anime_status" class="anime-select-field">
                <option value=""><?php _e('اختر الحالة', 'anime-manager'); ?></option>
                <option value="مستمر" <?php selected($anime_status, 'مستمر'); ?>><?php _e('مستمر', 'anime-manager'); ?></option>
                <option value="متوقف" <?php selected($anime_status, 'متوقف'); ?>><?php _e('متوقف', 'anime-manager'); ?></option>
                <option value="ملغي" <?php selected($anime_status, 'ملغي'); ?>><?php _e('ملغي', 'anime-manager'); ?></option>
                <option value="مكتمل" <?php selected($anime_status, 'مكتمل'); ?>><?php _e('مكتمل', 'anime-manager'); ?></option>
                <option value="قادم لاحقًا" <?php selected($anime_status, 'قادم لاحقًا'); ?>><?php _e('قادم لاحقًا', 'anime-manager'); ?></option>
            </select>
        </div>

        <!-- عدد الحلقات -->
        <div class="anime-field-group">
            <label for="anime_episodes" class="anime-field-label">
                <span class="anime-field-icon">📺</span>
                <?php _e('عدد الحلقات', 'anime-manager'); ?>
            </label>
            <input type="number" 
                   id="anime_episodes" 
                   name="_anime_episodes" 
                   value="<?php echo esc_attr($anime_episodes); ?>" 
                   class="anime-number-field"
                   min="1" 
                   max="9999"
                   placeholder="<?php _e('مثال: 12', 'anime-manager'); ?>">
        </div>

        <!-- الموسم -->
        <div class="anime-field-group">
            <label for="anime_season" class="anime-field-label">
                <span class="anime-field-icon">🗓️</span>
                <?php _e('الموسم', 'anime-manager'); ?>
            </label>
            <select id="anime_season" name="_anime_season" class="anime-select-field">
                <option value=""><?php _e('اختر الموسم', 'anime-manager'); ?></option>
                <option value="شتاء" <?php selected($anime_season, 'شتاء'); ?>><?php _e('شتاء', 'anime-manager'); ?></option>
                <option value="ربيع" <?php selected($anime_season, 'ربيع'); ?>><?php _e('ربيع', 'anime-manager'); ?></option>
                <option value="صيف" <?php selected($anime_season, 'صيف'); ?>><?php _e('صيف', 'anime-manager'); ?></option>
                <option value="خريف" <?php selected($anime_season, 'خريف'); ?>><?php _e('خريف', 'anime-manager'); ?></option>
            </select>
        </div>

        <!-- السنة -->
        <div class="anime-field-group">
            <label for="anime_year" class="anime-field-label">
                <span class="anime-field-icon">📅</span>
                <?php _e('السنة', 'anime-manager'); ?>
            </label>
            <input type="number" 
                   id="anime_year" 
                   name="_anime_year" 
                   value="<?php echo esc_attr($anime_year); ?>" 
                   class="anime-number-field"
                   min="1950" 
                   max="<?php echo date('Y') + 5; ?>"
                   placeholder="<?php echo date('Y'); ?>">
        </div>
    </div>

    <!-- الروابط الخارجية -->
    <div class="anime-field-group anime-links-section">
        <label class="anime-field-label anime-links-header">
            <span class="anime-field-icon">🔗</span>
            <?php _e('الروابط الخارجية', 'anime-manager'); ?>
        </label>
        <p class="anime-field-description">
            <?php _e('أضف روابط خارجية مثل موقع الأنمي الرسمي، صفحة الويكي، أو مواقع قواعد البيانات', 'anime-manager'); ?>
        </p>
        
        <div id="anime-links-container" class="anime-links-container">
            <?php if (!empty($anime_links) && is_array($anime_links)): ?>
                <?php foreach ($anime_links as $index => $link): ?>
                    <div class="anime-link-item">
                        <div class="anime-link-inputs">
                            <input type="text" 
                                   name="_anime_links[<?php echo $index; ?>][title]" 
                                   value="<?php echo esc_attr($link['title']); ?>" 
                                   placeholder="<?php _e('عنوان الرابط (مثال: الموقع الرسمي)', 'anime-manager'); ?>"
                                   class="anime-link-title">
                            <input type="url" 
                                   name="_anime_links[<?php echo $index; ?>][url]" 
                                   value="<?php echo esc_attr($link['url']); ?>" 
                                   placeholder="<?php _e('https://example.com', 'anime-manager'); ?>"
                                   class="anime-link-url">
                        </div>
                        <button type="button" class="anime-remove-link" title="<?php _e('حذف الرابط', 'anime-manager'); ?>">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <button type="button" id="anime-add-link" class="button button-secondary anime-add-link-btn">
            <span class="dashicons dashicons-plus-alt"></span>
            <?php _e('إضافة رابط جديد', 'anime-manager'); ?>
        </button>
    </div>

    <!-- نصائح مفيدة -->
    <div class="anime-tips-section">
        <h4><?php _e('💡 نصائح مفيدة:', 'anime-manager'); ?></h4>
        <ul class="anime-tips-list">
            <li><?php _e('استخدم خاصية "الصورة المميزة" لإضافة صورة غلاف الأنمي', 'anime-manager'); ?></li>
            <li><?php _e('اكتب القصة في المحرر الرئيسي أعلاه', 'anime-manager'); ?></li>
            <li><?php _e('يمكنك إضافة عدة روابط خارجية مثل الموقع الرسمي وصفحات قواعد البيانات', 'anime-manager'); ?></li>
            <li><?php _e('جميع الحقول اختيارية - املأ ما تريد فقط', 'anime-manager'); ?></li>
        </ul>
    </div>
</div>
