/**
 * تنسيقات مخصصة لصفحات الأنمي المفردة
 * تصميم عصري متكامل لكلا العرضين (السينغل والإضافة)
 */

/* إخفاء العناصر غير المرغوب فيها في صفحات الأنمي */
.single-anime .thumbnail-wrapper,
.single-anime .entry-header.single-entry-header,
.single-anime .post-thumbnail,
.single-anime .entry-meta.author-meta,
.single-anime .entry-title.single-entry-title {
    display: none !important;
}

/* تصميم عصري مشترك لكلا العرضين */
.anime-modern-container {
    --anime-primary: #6c5b7b;
    --anime-secondary: #f67280;
    --anime-accent: #355c7d;
    --anime-text: #131315;
    --anime-bg: #ffffff;
    --anime-card-bg: rgba(255, 255, 255, 0.9);
    --anime-border-radius: 12px;
    --anime-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --anime-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .anime-modern-container {
        --anime-text: #ffffff;
        --anime-bg: #131315;
        --anime-card-bg: rgba(19, 19, 21, 0.9);
    }
}

/* الحاوي الرئيسي لصفحة الأنمي */
.anime-single-page {
    margin: 0;
    padding: 0;
}

.anime-post-container {
    margin: 0;
    padding: 0;
    max-width: 100%;
}

.anime-single-article {
    margin: 0;
    padding: 0;
}

/* الصورة الرئيسية بحجم كامل */
.anime-hero-image {
    position: relative;
    width: 100vw;
    height: 100vh;
    margin-left: calc(-50vw + 50%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.anime-cover-full {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}

/* العنوان فوق الصورة */
.anime-title-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 4rem 2rem 2rem;
    z-index: 2;
}

.anime-title-container {
    max-width: var(--site-max-width, 1200px);
    margin: 0 auto;
}

.anime-main-title {
    color: var(--white, #ffffff);
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: var(--heading-font-weight, 700);
    font-family: var(--title-font, "Jost", Arial, Helvetica, sans-serif);
    margin: 0;
    text-shadow: 0 2px 10px rgba(0,0,0,0.7);
    line-height: 1.2;
}

/* محتوى الأنمي */
.anime-content-wrapper {
    max-width: var(--site-max-width, 1200px);
    margin: 0 auto;
    padding: var(--post-margin, 2.5rem) var(--wrapper-side-gutter, 2.5rem);
}

/* قسم التفاصيل المخصص */
.anime-details-section {
    background: var(--white, #ffffff);
    border-radius: var(--default-border-radius, 5px);
    box-shadow: var(--post-box-shadow, 0 0 20px rgba(0,0,0,0.07));
    padding: var(--card-padding, 1.875rem);
    margin-bottom: var(--post-margin, 2.5rem);
    border: 1px solid var(--default-border-color, rgba(0,0,0,0.08));
}

.anime-details-grid-custom {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* عناصر التفاصيل */
.anime-detail-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background: var(--very-light-grey, #f2f2f3);
    border-radius: var(--default-border-radius, 5px);
    border-right: 4px solid var(--primary-theme-color, #6c5b7b);
    transition: all 0.3s ease;
}

.anime-detail-item:hover {
    background: var(--off-white, #f7f8fa);
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.anime-detail-icon {
    font-size: 1.5rem;
    width: 30px;
    text-align: center;
    flex-shrink: 0;
}

.anime-detail-label {
    font-weight: 600;
    color: var(--body-font-color, #131315);
    font-family: var(--title-font, "Jost", Arial, Helvetica, sans-serif);
    min-width: 100px;
    flex-shrink: 0;
}

.anime-detail-value {
    font-weight: 500;
    color: var(--dark-grey, #44464b);
    flex: 1;
}

/* ألوان مخصصة للأنواع والحالات */
.anime-type-تلفازي,
.anime-status-مستمر {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.anime-type-بلوراي,
.anime-status-مكتمل {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.anime-type-ويب {
    color: #7c3aed;
    background: rgba(124, 58, 237, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.anime-status-متوقف,
.anime-status-ملغي {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.anime-status-قادم-لاحقًا {
    color: #d97706;
    background: rgba(217, 119, 6, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

/* قسم الروابط المخصص */
.anime-links-section-custom {
    border-top: 1px solid var(--default-border-color, rgba(0,0,0,0.08));
    padding-top: 2rem;
    margin-top: 2rem;
}

.anime-links-title-custom {
    margin: 0 0 1.5rem 0;
    font-size: var(--h4-font-size, 1.5rem);
    font-weight: var(--heading-font-weight, 700);
    color: var(--body-font-color, #131315);
    font-family: var(--title-font, "Jost", Arial, Helvetica, sans-serif);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.anime-links-grid-custom {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

/* الروابط الخارجية المخصصة */
.anime-external-link-custom {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
    background: var(--white, #ffffff);
    border: 2px solid var(--default-border-color, rgba(0,0,0,0.08));
    border-radius: var(--default-border-radius, 5px);
    text-decoration: none;
    color: var(--body-font-color, #131315);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.anime-external-link-custom::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-theme-color, #6c5b7b), var(--secondary-theme-color, #f67280));
    transition: width 0.3s ease;
    z-index: 0;
}

.anime-external-link-custom:hover::before {
    width: 100%;
}

.anime-external-link-custom:hover {
    color: var(--white, #ffffff);
    border-color: var(--primary-theme-color, #6c5b7b);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.anime-link-icon-custom,
.anime-link-text-custom,
.anime-link-arrow-custom {
    position: relative;
    z-index: 1;
}

.anime-link-icon-custom {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.anime-link-text-custom {
    flex: 1;
    font-weight: 500;
    font-size: 1rem;
}

.anime-link-arrow-custom {
    font-size: 1.25rem;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.anime-external-link-custom:hover .anime-link-arrow-custom {
    transform: translate(3px, -3px);
}

/* محتوى القصة */
.anime-story-content {
    background: var(--white, #ffffff);
    border-radius: var(--default-border-radius, 5px);
    box-shadow: var(--post-box-shadow, 0 0 20px rgba(0,0,0,0.07));
    padding: var(--card-padding, 1.875rem);
    margin-bottom: var(--post-margin, 2.5rem);
    border: 1px solid var(--default-border-color, rgba(0,0,0,0.08));
}

.anime-story-content .entry-content {
    font-size: var(--single-entry-font-size, 1.375rem);
    line-height: 1.7;
    color: var(--body-font-color, #131315);
}

.anime-story-content .entry-content p {
    margin-bottom: 1.5rem;
}

.anime-story-content .entry-content h2,
.anime-story-content .entry-content h3,
.anime-story-content .entry-content h4 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-theme-color, #6c5b7b);
}

/* التجاوب مع الشاشات المختلفة */
@media (max-width: 1024px) {
    .anime-hero-image {
        height: 70vh;
    }
    
    .anime-details-grid-custom {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .anime-content-wrapper {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 768px) {
    .anime-hero-image {
        height: 60vh;
    }
    
    .anime-title-overlay {
        padding: 2rem 1rem 1rem;
    }
    
    .anime-main-title {
        font-size: clamp(1.5rem, 4vw, 2.5rem);
    }
    
    .anime-details-grid-custom {
        grid-template-columns: 1fr;
    }
    
    .anime-detail-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
        padding: 1.5rem;
    }
    
    .anime-detail-label {
        min-width: auto;
    }
    
    .anime-links-grid-custom {
        grid-template-columns: 1fr;
    }
    
    .anime-content-wrapper {
        padding: 1.5rem 1rem;
    }
}

@media (max-width: 480px) {
    .anime-hero-image {
        height: 50vh;
    }
    
    .anime-external-link-custom {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .anime-details-section,
    .anime-story-content {
        padding: 1.25rem;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .anime-details-section,
    .anime-story-content {
        background: var(--very-dark-grey, #131315);
        border-color: var(--dark-grey, #44464b);
    }
    
    .anime-detail-item {
        background: var(--dark-grey, #44464b);
        color: var(--white, #ffffff);
    }
    
    .anime-detail-item:hover {
        background: var(--medium-grey, #94979e);
    }
    
    .anime-external-link-custom {
        background: var(--very-dark-grey, #131315);
        border-color: var(--dark-grey, #44464b);
        color: var(--white, #ffffff);
    }
}

/* تأثيرات الدخول */
.anime-details-section,
.anime-story-content {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* إخفاء العرض الافتراضي للإضافة في صفحات الأنمي المخصصة */
.single-anime .anime-details-card {
    display: none !important;
}
