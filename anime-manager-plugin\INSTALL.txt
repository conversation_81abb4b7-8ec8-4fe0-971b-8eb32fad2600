تعليمات تثبيت إضافة مدير الأنمي
=====================================

🎯 خطوات التثبيت السريع:

1. انسخ مجلد "anime-manager-plugin" بالكامل
2. الصق المجلد في: wp-content/plugins/
3. غير اسم المجلد إلى: anime-manager
4. اذهب إلى لوحة تحكم ووردبريس
5. اضغط على "الإضافات" في القائمة الجانبية
6. ابحث عن "مدير الأنمي - Anime Manager"
7. اضغط "تفعيل"

🔧 التحقق من التثبيت:

بعد التفعيل، يجب أن ترى:
- قائمة "إدارة الأنمي" في لوحة التحكم
- أيقونة فيديو بجانب اسم القائمة
- إمكانية إضافة أنمي جديد

📁 هيكل الملفات النهائي:

wp-content/
└── plugins/
    └── anime-manager/
        ├── anime-manager.php
        ├── README.md
        ├── INSTALL.txt
        ├── includes/
        │   ├── metabox-template.php
        │   └── frontend-display.php
        └── assets/
            ├── admin-style.css
            ├── admin-script.js
            └── frontend-style.css

⚠️ ملاحظات مهمة:

- تأكد من أن اسم المجلد هو "anime-manager" بالضبط
- لا تغير أسماء الملفات الداخلية
- تأكد من رفع جميع الملفات والمجلدات
- يتطلب PHP 7.4 أو أحدث
- يتطلب ووردبريس 5.0 أو أحدث

🚀 الاستخدام الأول:

1. اذهب إلى "إدارة الأنمي" > "إضافة جديد"
2. أدخل عنوان الأنمي
3. اكتب القصة في المحرر
4. أضف صورة الغلاف
5. املأ التفاصيل في الصندوق أسفل المحرر
6. انشر الأنمي
7. اذهب لمشاهدة النتيجة في الموقع

✅ اختبار سريع:

لاختبار أن كل شيء يعمل:
1. أنشئ أنمي تجريبي
2. أضف بعض التفاصيل
3. انشر الأنمي
4. اذهب لصفحة الأنمي في الموقع
5. يجب أن ترى بطاقة جميلة بتفاصيل الأنمي

🆘 في حالة وجود مشاكل:

- تأكد من تفعيل الإضافة
- امسح ذاكرة التخزين المؤقت
- تحقق من سجل الأخطاء
- تأكد من صلاحيات الملفات
- جرب إلغاء تفعيل الإضافات الأخرى مؤقتاً

📞 للدعم:

راجع ملف README.md للتفاصيل الكاملة والدعم الفني.

نتمنى لك تجربة رائعة مع إضافة مدير الأنمي! 🎌
