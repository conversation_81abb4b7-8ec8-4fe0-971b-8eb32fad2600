<?php


/**
 * Page Settings
 *
 * @package WordPress
 * @subpackage jinko
 */

function tfm_customize_register_page( $wp_customize ) {

	$customizer_settings = tfm_general_settings();

	$wp_customize->add_section( 'tfm_page_settings', array(
		'title'    => esc_html__( 'Page Settings', 'jinko' ),
		'priority' => 130,
		'panel' => 'tfm_theme_settings',
	) );

	// Sidebar
	$wp_customize->add_setting( 'tfm_page_sidebar', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_page_sidebar', array(
		'label'       => esc_html__( 'Display Sidebar', 'jinko' ),
		'section'     => 'tfm_page_settings',
		'type'        => 'checkbox',
	) );

	// Sidebar position
	$wp_customize->add_setting( 'tfm_page_sidebar_position', array(
		'default'           => 'side',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_page_sidebar_position', array(
		'label'       => esc_html__( 'Sidebar Position', 'jinko' ),
		'section'     => 'tfm_page_settings',
		'type'        => 'radio',
		'choices'     => array(
			'side' => esc_html__( 'Side', 'jinko' ),
			'after' => esc_html__( 'Below Featured Media', 'jinko' ),
		),
	) );

	// Layout style
	$wp_customize->add_setting( 'tfm_page_style', array(
		'default'           => 'default',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_page_style', array(
		'label'       => esc_html__( 'Page Style', 'jinko' ),
		'section'     => 'tfm_page_settings',
		'type'        => 'select',
		'choices'     => array(
			'default' => esc_html__( 'Classic', 'jinko' ),
			'default-alt' => esc_html__( 'Classic Alt.', 'jinko' ),
			'cover' => esc_html__( 'Cover', 'jinko' ),
		),
	) );

	// Image aspect ratio
	$wp_customize->add_setting( 'tfm_page_thumbnail_aspect_ratio', array(
		'default'           => 'hero',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_page_thumbnail_aspect_ratio', array(
		'label'       => esc_html__( 'Thumbnail Aspect Ratio', 'jinko' ),
		'section'     => 'tfm_page_settings',
		'type'        => 'select',
		'choices'     => array(
			'wide' => esc_html__( 'Wide', 'jinko' ),
			'landscape' => esc_html__( 'Landscape', 'jinko' ),
			'portrait' => esc_html__( 'Portrait', 'jinko' ),
			'square' => esc_html__( 'Square', 'jinko' ),
			'hero' => esc_html__( 'Hero', 'jinko' ),
			'uncropped' => esc_html__( 'Uncropped', 'jinko' ),
		),
	) );

	if ( apply_filters( 'tfm_theme_supports_full_width_posts', true )) :

		$wp_customize->add_setting( 'tfm_page_full_width', array(
			'default'           => false,
			'sanitize_callback' => 'tfm_sanitize_checkbox',
		) );

		$wp_customize->add_control( 'tfm_page_full_width', array(
			'label'       => esc_html__( 'Full Width', 'jinko' ),
			'section'     => 'tfm_page_settings',
			'type'        => 'checkbox',
		) );

	endif;

	$wp_customize->add_setting( 'tfm_page_thumbnail', array(
			'default'           => true,
			'sanitize_callback' => 'tfm_sanitize_checkbox',
		) );

		$wp_customize->add_control( 'tfm_page_thumbnail', array(
			'label'       => esc_html__( 'Thumbnail', 'jinko' ),
			'section'     => 'tfm_page_settings',
			'type'        => 'checkbox',
		) );

	$wp_customize->add_setting( 'tfm_front_page_entry_title', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_front_page_entry_title', array(
		'label'       => esc_html__( 'Show Page Title on Front Page', 'jinko' ),
		'description' => esc_html__( 'If your Homepage is set to a page', 'jinko'),
		'section'     => 'tfm_page_settings',
		'type'        => 'checkbox',
	) );

}

add_action( 'customize_register', 'tfm_customize_register_page' );