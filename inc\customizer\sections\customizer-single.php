<?php


/**
 * Single Settings
 *
 * @package WordPress
 * @subpackage jinko
 */

function tfm_customize_register_single( $wp_customize ) {

	$customizer_settings = tfm_general_settings();

	// ========================================================
	// Single Post Settings
	// ========================================================

	$wp_customize->add_section( 'tfm_single_settings', array(
		'title'    => esc_html__( 'Single Post Settings', 'jinko' ),
		'priority' => 130,
		'panel' => 'tfm_theme_settings',
	) );

	// Sidebar
	$wp_customize->add_setting( 'tfm_single_sidebar', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_sidebar', array(
		'label'       => esc_html__( 'Display Sidebar', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Sidebar position
	$wp_customize->add_setting( 'tfm_single_sidebar_position', array(
		'default'           => 'side',
		'sanitize_callback' => 'tfm_sanitize_radio',
	) );

	$wp_customize->add_control( 'tfm_single_sidebar_position', array(
		'label'       => esc_html__( 'Sidebar Position', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'radio',
		'choices'     => array(
			'side' => esc_html__( 'Side', 'jinko' ),
			'after' => esc_html__( 'Below Featured Media', 'jinko' ),
		),
	) );

	// Post style
	$wp_customize->add_setting( 'tfm_single_post_style', array(
		'default'           => 'default',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_single_post_style', array(
		'label'       => esc_html__( 'Post Style', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'select',
		'choices'     => array(
			'default' => esc_html__( 'Classic', 'jinko' ),
			'default-alt' => esc_html__( 'Classic Alt.', 'jinko' ),
			'cover' => esc_html__( 'Cover', 'jinko' ),
		),
	) );

	// Image aspect ratio
	$wp_customize->add_setting( 'tfm_single_thumbnail_aspect_ratio', array(
		'default'           => 'hero',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_single_thumbnail_aspect_ratio', array(
		'label'       => esc_html__( 'Thumbnail Aspect Ratio', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'select',
		'choices'     => array(
			'wide' => esc_html__( 'Wide', 'jinko' ),
			'landscape' => esc_html__( 'Landscape', 'jinko' ),
			'portrait' => esc_html__( 'Portrait', 'jinko' ),
			'square' => esc_html__( 'Square', 'jinko' ),
			'hero' => esc_html__( 'Hero', 'jinko' ),
			'uncropped' => esc_html__( 'Uncropped', 'jinko' ),
		),
	) );

	// Excerpt
	$wp_customize->add_setting( 'tfm_single_thumbnail', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_thumbnail', array(
		'label'       => esc_html__( 'Thumbnail', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	if ( apply_filters( 'tfm_theme_supports_full_width_posts', true )) :

		// Full width
		$wp_customize->add_setting( 'tfm_single_full_width', array(
			'default'           => false,
			'sanitize_callback' => 'tfm_sanitize_checkbox',
		) );

		$wp_customize->add_control( 'tfm_single_full_width', array(
			'label'       => esc_html__( 'Full Width', 'jinko' ),
			'section'     => 'tfm_single_settings',
			'type'        => 'checkbox',
		) );

	endif;

	// Excerpt
	$wp_customize->add_setting( 'tfm_single_custom_excerpt', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_custom_excerpt', array(
		'label'       => esc_html__( 'Display Custom excerpt', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Show by meta
	$wp_customize->add_setting( 'tfm_single_entry_meta_by', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_entry_meta_by', array(
		'label'       => esc_html__( '"by"', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Show by meta
	$wp_customize->add_setting( 'tfm_single_entry_meta_in', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_entry_meta_in', array(
		'label'       => esc_html__( '"in"', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_entry_meta_author', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_author', array(
		'label'       => esc_html__( 'Author', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_entry_meta_author_avatar', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_author_avatar', array(
		'label'       => esc_html__( 'Avatar', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_entry_meta_author_nickname', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_author_nickname', array(
		'label'       => esc_html__( 'Nickname', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_entry_meta_category', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_category', array(
		'label'       => esc_html__( 'Category', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_entry_meta_date', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_date', array(
		'label'       => esc_html__( 'Date', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_entry_meta_date_updated', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_date_updated', array(
		'label'       => esc_html__( 'Updated Date', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	if ( function_exists( 'tfm_read_time') ) {

		$wp_customize->add_setting( 'tfm_single_entry_meta_read_time', array(
			'default'           => true,
			'sanitize_callback' => 'tfm_sanitize_checkbox',
		) );

		$wp_customize->add_control( 'tfm_single_entry_meta_read_time', array(
			'label'       => esc_html__( 'Read Time', 'jinko' ),
			'section'     => 'tfm_single_settings',
			'type'        => 'checkbox',
		) );

	}

	$wp_customize->add_setting( 'tfm_single_entry_meta_comment_count', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_entry_meta_comment_count', array(
		'label'       => esc_html__( 'Comment Count', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_post_tags', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_tags', array(
		'label'       => esc_html__( 'Post Tags', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	$wp_customize->add_setting( 'tfm_single_post_tags_count', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_tags_count', array(
		'label'       => esc_html__( 'Post Tag Count', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	/**
	Separator
	**/
	$wp_customize->add_setting('tfm_single_post_navigation_separator', array(
		'default'           => '',
		'sanitize_callback' => 'esc_html',
	));
	$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_single_post_navigation_separator', array(
		'settings'		=> 'tfm_single_post_navigation_separator',
		'section'  		=> 'tfm_single_settings',
	)));

	// Prev/Next
	$wp_customize->add_setting( 'tfm_single_post_navigation', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation', array(
		'label'       => esc_html__( 'Previous/Next Post', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Prev/next layout
	$wp_customize->add_setting( 'tfm_single_post_navigation_layout', array(
		'default'           => 'grid',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation_layout', array(
		'label'       => esc_html__( 'Prev/Next Post Layout', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'select',
		'choices'     => array(
			'grid' => esc_html__( 'Grid', 'jinko' ),
			'list' => esc_html__( 'List', 'jinko' ),
		),
	) );

	// Prev/next style
	$wp_customize->add_setting( 'tfm_single_post_navigation_style', array(
		'default'           => 'default',
		'sanitize_callback' => 'tfm_sanitize_select',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation_style', array(
		'label'       => esc_html__( 'Prev/Next Post Style', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'select',
		'choices'     => array(
			'default' => esc_html__( 'Classic', 'jinko' ),
			'card' => esc_html__( 'Card', 'jinko' ),
			'cover' => esc_html__( 'Cover', 'jinko' ),
		),
	) );

	// Prev/Next Thumbnail
	$wp_customize->add_setting( 'tfm_single_post_navigation_entry_title', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation_entry_title', array(
		'label'       => esc_html__( 'Prev/Next Entry Title', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );


	// Prev/Next Thumbnail
	$wp_customize->add_setting( 'tfm_single_post_navigation_thumbnail', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation_thumbnail', array(
		'label'       => esc_html__( 'Prev/Next Post Thumbnail', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Prev/Next Thumbnail
	$wp_customize->add_setting( 'tfm_single_post_navigation_excerpt', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation_excerpt', array(
		'label'       => esc_html__( 'Prev/Next Post Excerpt', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Prev/Next Thumbnail
	// $wp_customize->add_setting( 'tfm_single_post_navigation_category', array(
	// 	'default'           => false,
	// 	'sanitize_callback' => 'tfm_sanitize_checkbox',
	// ) );

	// $wp_customize->add_control( 'tfm_single_post_navigation_category', array(
	// 	'label'       => esc_html__( 'Prev/Next Post Category', 'jinko' ),
	// 	'section'     => 'tfm_single_settings',
	// 	'type'        => 'checkbox',
	// ) );

	// Prev/Next Thumbnail
	$wp_customize->add_setting( 'tfm_single_post_navigation_header', array(
		'default'           => false,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	$wp_customize->add_control( 'tfm_single_post_navigation_header', array(
		'label'       => esc_html__( 'Prev/Next Post Header', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	/**
	Separator
	**/
	$wp_customize->add_setting('tfm_single_post_author_bio_separator', array(
		'default'           => '',
		'sanitize_callback' => 'esc_html',
	));
	$wp_customize->add_control(new Tfm_Separator_Custom_Control($wp_customize, 'tfm_single_post_author_bio_separator', array(
		'settings'		=> 'tfm_single_post_author_bio_separator',
		'section'  		=> 'tfm_single_settings',
	)));

	// Show Author Bio Avatar
	$wp_customize->add_setting( 'tfm_single_author_bio_avatar', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_author_bio_avatar', array(
		'label'       => esc_html__( 'Author Bio Avatar', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Show Author Bio Avatar
	$wp_customize->add_setting( 'tfm_single_author_bio_name', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_author_bio_name', array(
		'label'       => esc_html__( 'Author Bio Name', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Show Author Bio Avatar
	$wp_customize->add_setting( 'tfm_single_author_bio_info', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_author_bio_info', array(
		'label'       => esc_html__( 'Author Bio Info', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

	// Toggle Comments
	$wp_customize->add_setting( 'tfm_single_toggle_comments', array(
		'default'           => true,
		'sanitize_callback' => 'tfm_sanitize_checkbox',
	) );

	// Control Options
	$wp_customize->add_control( 'tfm_single_toggle_comments', array(
		'label'       => esc_html__( 'Click Button to Open Comments', 'jinko' ),
		'section'     => 'tfm_single_settings',
		'type'        => 'checkbox',
	) );

}

add_action( 'customize_register', 'tfm_customize_register_single' );