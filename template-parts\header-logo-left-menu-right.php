<?php
/**
 * Template part for displaying the header
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

?>

<div class="site-header-inner">
<div class="header-layout-wrapper">
<div class="header-section header-left header-branding">

<?php tfm_toggle_icon( 'menu' ); ?>

<?php tfm_site_logo( ); ?>

<?php if ( get_theme_mod( 'tfm_header_tagline', false ) && get_bloginfo( 'description' ) ) : ?>

		<div class="tagline"><?php echo get_bloginfo( 'description' ); ?></div>

	<?php endif; ?>

<?php tfm_header_left(); ?>

</div>

<?php if ( has_nav_menu( 'primary' ) ) : ?>

	    <?php

	    wp_nav_menu( array( 'theme_location' => 'primary',
	     					'container' => false,
	     					'menu_class' => 'primary-menu',
	     					'menu_id' => 'primary-menu',
	     					'link_before' => '<span class="menu-label">',
	     					'link_after' => '</span>'));

	    ?>

	<?php endif; ?>

	<div class="header-section header-right">

		<?php tfm_header_right(); ?>
		 
	<?php tfm_toggle_icon( 'color-mode' );
		  tfm_toggle_icon( 'search' ); ?>

	<?php if ( has_nav_menu( 'header-secondary' ) ) :

		    $cta_background = get_theme_mod( 'tfm_cta_background', '' ) ? ' has-cta-background' : '';

		    wp_nav_menu( array( 'theme_location' => 'header-secondary',
		     					'container' => false,
		     					'container_class' => 'header-secondary-menu-wrapper',
		     					'menu_class' => 'primary-menu header-secondary' . $cta_background,
		     					'menu_id' => 'header-secondary-menu',
			     				'link_before' => '<span class="menu-label">',
		     					'link_after' => '</span>')); 

		    endif; ?>

</div>
</div>
</div>