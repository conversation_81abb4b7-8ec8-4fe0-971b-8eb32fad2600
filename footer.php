<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

$tfm_vars = tfm_template_vars('', false);

tfm_before_wrapper_close();

?>
</div><!-- wrap-inner -->
</div><!-- .wrap -->

<?php

// Before Footer Hook
tfm_before_footer();

?>

<?php

	$columns = array(is_active_sidebar( 'footer-column-1' ), is_active_sidebar( 'footer-column-2' ), is_active_sidebar( 'footer-column-3' ), ( is_active_sidebar( 'footer-column-4' ) && ! has_nav_menu( 'footer' ) ), has_nav_menu( 'footer' ));
	$cols = count(array_filter($columns)) > 4 ? 'cols-4' : 'cols-' . count(array_filter($columns));

?>

		<footer id="colophon" class="site-footer<?php echo esc_attr( $tfm_vars['footer_classes']); ?>">

			<div class="site-footer-inner">

				<?php 

				if ( is_active_sidebar( 'footer-column-1' ) ||
				 is_active_sidebar( 'footer-column-2' ) ||
				 is_active_sidebar( 'footer-column-3' ) ||
				 is_active_sidebar( 'footer-column-4' ) ||
				 has_nav_menu( 'footer' ) ) : ?>

				<div class="footer-widget-area footer-columns <?php echo esc_attr( $cols ); ?>">
					<?php
					if ( is_active_sidebar( 'footer-column-1' ) ) : ?>
						<div class="footer-column footer-column-1 sidebar">
							<?php dynamic_sidebar( 'footer-column-1' ); ?>
						</div>
					<?php endif;
					if ( is_active_sidebar( 'footer-column-2' ) ) : ?>
						<div class="footer-column footer-column-2 sidebar">
							<?php dynamic_sidebar( 'footer-column-2' ); ?>
						</div>
					<?php endif;
					if ( is_active_sidebar( 'footer-column-3' ) ) : ?>
						<div class="footer-column footer-column-3 sidebar">
							<?php dynamic_sidebar( 'footer-column-3' ); ?>
						</div>
					<?php endif;
					if ( is_active_sidebar( 'footer-column-4' ) && ! has_nav_menu( 'footer' ) ) : ?>
						<div class="footer-column footer-column-4 sidebar">
							<?php dynamic_sidebar( 'footer-column-4' ); ?>
						</div>
					<?php endif;
					if ( has_nav_menu( 'footer' ) ) : ?>
						<div class="footer-column footer-column-4 footer-menu">
						    <?php

						    wp_nav_menu( array( 'theme_location' => 'footer',
						     					 'container' => 'ul',
						     					 'depth' => 2,
						     					 'menu_class' => 'footer-nav',
						     					 'menu_id' => 'footer-nav')); ?>
						</div>
					<?php endif; ?>
				</div><!-- .widget-area -->

			<?php endif; ?>

			<?php tfm_footer_inner(); ?>

			<?php if ( get_theme_mod( 'tfm_footer_text', get_bloginfo('description')) || ( function_exists('tfm_social_icons_theme_footer') && get_theme_mod( 'tfm_footer_social', false ) )): ?>

			<div class="footer-bottom">

					<div class="footer-copyright">
						<?php if (get_theme_mod( 'tfm_footer_logo_upload', '')):
						 tfm_site_logo( array( 'footer' => true ) );
						 endif; ?>
						<?php echo wp_kses_post( get_theme_mod( 'tfm_footer_text', get_bloginfo('description')) ); ?>
						<?php tfm_footer_copyright(); // hook ?>
					</div>

				<?php

				// Append Footer Bottom
				tfm_append_footer_bottom();

				?>

			</div>

			<?php endif; ?>

		</div><!-- .footer-inner -->
		</footer>
		<?php if ( get_theme_mod( 'tfm_goto_top', true ) ): ?>
			<a href="" class="goto-top backtotop"><span><?php echo esc_html__( 'back to top', 'jinko' ); ?></span></a>
		<?php endif; ?>
		
		<?php

		// After Footer Hook
		tfm_after_footer();

		?>
		
<?php wp_footer(); ?>

</body>
</html>
