<?php
/**
 * Template part for displaying prev/next post navigation
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package WordPress
 * @subpackage jinko
 * @since 1.0
 * @version 1.0
 */

?>

<?php

if ( false === get_theme_mod( 'tfm_single_post_navigation', true ) ) {
	return false;
}

$prev_post = get_previous_post();
$next_post = get_next_post();

$layout = get_theme_mod( 'tfm_single_post_navigation_layout', 'grid');
$mobile_layout = $layout === 'grid' ? ' mobile-grid' : ' mobile-compact';
$prev_next_post_style = get_theme_mod( 'tfm_single_post_navigation_style', 'default' );
$thumbnail_aspect_ratio = 'list' === $layout ? 'square' : 'wide';
$prev_thumbnail= ( $prev_post && get_theme_mod( 'tfm_single_post_navigation_thumbnail', false ) && '' !== get_the_post_thumbnail( $prev_post->ID ) ? ' has-post-media has-post-thumbnail thumbnail-' . $thumbnail_aspect_ratio : '' );
$next_thumbnail = ( $next_post && get_theme_mod( 'tfm_single_post_navigation_thumbnail', false ) && '' !== get_the_post_thumbnail( $next_post->ID ) ? ' has-post-media has-post-thumbnail thumbnail-' . $thumbnail_aspect_ratio : '' );
$thumb_size = $layout === 'list' ? 'thumbnail' : 'tfm-small-image';
$has_section_header = get_theme_mod( 'tfm_single_post_navigation_header', true ) ? ' has-header' : '';
$prev_next_post_style = 'list' === $layout && 'cover' === $prev_next_post_style ? 'default' : $prev_next_post_style;
if ( 'card' === $prev_next_post_style ) {
	$prev_next_post_style = $prev_next_post_style . ' has-background';
}
$cols = $layout === 'list' && (( $prev_post && ! $next_post ) || ( $next_post && ! $prev_post)) ? 1 : 2;
$has_prev_post = $prev_post ? ' has-prev-post' : '';
$has_next_post = $next_post ? ' has-next-post' : '';
$has_excerpt = get_theme_mod( 'tfm_single_post_navigation_excerpt', false ) ? ' has-excerpt' : false;
$has_category = get_theme_mod( 'tfm_single_post_navigation_category', false ) ? ' has-category' : false;
$has_title = get_theme_mod( 'tfm_single_post_navigation_entry_title', true ) ? ' has-entry-title' : false;

if ( $prev_post || $next_post ) : ?>

<div class="content-area post-navigation post-grid <?php echo esc_attr( $layout . $mobile_layout . $has_section_header . $has_next_post . $has_prev_post ); ?> cols-<?php echo esc_attr( $cols ); ?>">
	<?php if ( get_theme_mod( 'tfm_single_post_navigation_header', false )): ?>
	<div class="section-header">
		<h3><?php echo esc_html__( 'More Reading', 'jinko' ); ?></h3>
	</div>
<?php endif; ?>
	<h2 class="screen-reader-text">Post navigation</h2>

<?php

	// ========================================================
	// Prev post
	// ========================================================

	 if ( $prev_post ): ?>

	<article class="article post hentry previous-article has-category-meta <?php echo esc_attr( $prev_thumbnail . $has_excerpt . $has_category . $has_title . ' ' . $prev_next_post_style  ); ?>">
		<div class="post-inner">
		<?php if ( $prev_thumbnail ) : ?>
			<div class="thumbnail-wrapper">
			<figure class="post-thumbnail">
	  				<a href="<?php the_permalink( $prev_post->ID ); ?>"><?php echo get_the_post_thumbnail( $prev_post->ID, $thumb_size ); ?></a>
	  	</figure>
	  	<?php if ( 'cover' !== $prev_next_post_style && 'list' !== $layout ): ?>
  				<span class="prev-next prev"><?php echo esc_html__( 'Previous Post', 'jinko' ); ?></span>
  	<?php endif; ?>
	  </div>
	  <?php endif; ?>


	  <?php if ( ( $layout === 'list' || $prev_next_post_style === 'cover' ) && '' !== $prev_thumbnail ): ?>
	  <div class="entry-wrapper">
	  <?php endif; ?>
  				<?php if ( get_theme_mod( 'tfm_single_post_navigation_category', false ) ) :
  				tfm_get_entry_meta( $meta_data = array('category', 'args' => array('num' => 1, 'post_id' => $prev_post->ID)), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = 'category-meta', $meta_class = 'categories' );
  				endif; ?>
  				<?php if ( get_theme_mod( 'tfm_single_post_navigation_entry_title', true ) ) : ?>
  			<h3 class="entry-title"><a href="<?php the_permalink( $prev_post->ID ); ?>"><?php echo wp_kses_post( $prev_post->post_title ); ?></a></h3>
  		<?php endif; ?>

  			<?php

  			// ========================================================
				// No thumb OR cover w/thumb
				// ========================================================

  			 if ( ! $prev_thumbnail || ( $prev_thumbnail && ( 'cover' === $prev_next_post_style || 'list' === $layout ) ) ) : ?>

  				<?php if ( strlen($prev_post->post_title) == 0 || false === $has_title ) : ?>

  					<h3 class="entry-title prev">

  				<?php else: ?>

  					<span class="prev-next prev entry-meta after-title">

  				<?php

  				endif;

  				if ( strlen($prev_post->post_title) == 0 || false === $has_title ) : ?>

           	<a href="<?php the_permalink( $prev_post->ID ); ?>">

  				<?php endif; ?>

  					<?php echo esc_html__( 'Previous Post', 'jinko' ); ?>

  				<?php if ( strlen($prev_post->post_title) == 0 || false === $has_title ) : ?>

           	</a>

  				<?php

  				endif; 

  				if ( strlen($prev_post->post_title) == 0 || false === $has_title ) : ?>

  					</h3>

  				<?php else: ?>

  					</span>

  				<?php endif; ?>

  			<?php endif; ?>

  				<?php if ( get_theme_mod( 'tfm_single_post_navigation_excerpt', false ) ): ?>

  				<?php tfm_get_excerpt( 14, $prev_post->ID ); ?>

  			<?php endif; ?>

  				<?php if ( ( $layout === 'list' || $prev_next_post_style === 'cover' ) && '' !== $prev_thumbnail ): ?>
  	</div>
  <?php endif; ?>
  	</div>
  </article>

<?php endif; ?>

<?php


	// ========================================================
	// Next post
	// ========================================================

	 if ( $next_post ): ?>

	<article class="article post hentry next-article has-category-meta <?php echo esc_attr( $next_thumbnail . $has_excerpt . $has_category . $has_title . ' ' . $prev_next_post_style  ); ?>">
		<div class="post-inner">
		<?php if ( $next_thumbnail ) : ?>
			<div class="thumbnail-wrapper">
		<figure class="post-thumbnail">

	  				<a href="<?php the_permalink( $next_post->ID ); ?>">

	  				<?php echo get_the_post_thumbnail( $next_post->ID, $thumb_size ); ?>

	  				</a>

	  	</figure>
	  	<?php if ( 'cover' !== $prev_next_post_style && 'list' !== $layout  ):  ?>
  				<span class="prev-next next"><?php echo esc_html__( 'Next Post', 'jinko' ); ?></span>
  	<?php endif; ?>
	  </div>
	  	<?php endif; ?>
	  	<?php if ( ( $layout === 'list' || $prev_next_post_style === 'cover' ) && '' !== $next_thumbnail ): ?>
	  <div class="entry-wrapper">
	  <?php endif; ?>
  				<?php  if ( get_theme_mod( 'tfm_single_post_navigation_category', false ) ) :
  					tfm_get_entry_meta( $meta_data = array('category', 'args' => array('num' => 1, 'post_id' => $next_post->ID)), $has_wrapper = true, $has_container = true, $html_style = 'li', $meta_wrapper_class = 'category-meta', $meta_class = 'categories' );
  					endif; ?>
  					<?php if ( get_theme_mod( 'tfm_single_post_navigation_entry_title', true ) ) : ?>
  			<h3 class="entry-title"><a href="<?php the_permalink( $next_post->ID ); ?>"><?php echo wp_kses_post( $next_post->post_title ); ?></a></h3>
  		<?php endif; ?>
  			<?php

  			// ========================================================
				// No thumb OR cover w/thumb
				// ========================================================

  			 if ( ! $next_thumbnail || ( $next_thumbnail && ( 'cover' === $prev_next_post_style || 'list' === $layout ) ) ) : ?>

  				<?php if ( strlen($next_post->post_title) == 0 || false === $has_title ) : ?>

  					<h3 class="entry-title next">

  				<?php else: ?>

  					<span class="prev-next next entry-meta after-title">

  				<?php

  				endif;

  				if ( strlen($next_post->post_title) == 0 || false === $has_title ) : ?>

           	<a href="<?php the_permalink( $next_post->ID ); ?>">

  				<?php endif; ?>

  					<?php echo esc_html__( 'Next Post', 'jinko' ); ?>

  				<?php if ( strlen($next_post->post_title) == 0 || false === $has_title ) : ?>

           	</a>

  				<?php

  				endif; 

  				if ( strlen($next_post->post_title) == 0 || false === $has_title ) : ?>

  					</h3>

  				<?php else: ?>

  					</span>

  				<?php endif; ?>

  			<?php endif; ?>

  				<?php if ( get_theme_mod( 'tfm_single_post_navigation_excerpt', false ) ): ?>

  				<?php tfm_get_excerpt( 14, $next_post->ID); ?>

  			<?php endif; ?>

  	<?php if ( ( $layout === 'list' || $prev_next_post_style === 'cover' ) && '' !== $next_thumbnail ): ?>
	  </div>
	  <?php endif; ?>
  	</div>
  	</article>

<?php endif; ?>

</div>

<?php endif; ?>